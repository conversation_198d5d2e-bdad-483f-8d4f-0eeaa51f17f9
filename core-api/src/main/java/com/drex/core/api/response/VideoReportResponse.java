package com.drex.core.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 事件上报响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoReportResponse implements Serializable {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 当前进度信息 第一阶段的奖励区间结束后立刻变为第二阶段
     */
    private Integer playProgress;

    /**
     * 奖励信息
     */
    private RewardInfo rewardInfo;

    /**
     * 奖励信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RewardInfo implements Serializable {
        /**
         * 奖励代码
         */
        private String rewardCode;

        /**
         * 奖励值
         */
        private Long rewardAmount;

        /**
         * 奖励等级
         */
        private String rewardLevel;

        /**
         * 进度等级
         */
        private Integer progress;

        /**
         * 奖励状态 UNCLAIMED, CLAIMED, EXPIRED
         */
        private String rewardStatus;

    }
}
