package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.random.R;
import org.springframework.stereotype.Component;

import java.util.Arrays;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class MaizeRecordBuilderImpl extends WideColumnStoreBuilder<MaizeRecord> implements MaizeRecordBuilder {

    @Resource
    private IdUtils idUtils;

    @PostConstruct
    public void init() {
        super.init(MaizeRecord.class);
    }

    @Override
    public String tableName() {
        return "maize_record";
    }

    @Override
    public boolean save(MaizeRecord maizeRecord) {
        return super.putRow(maizeRecord, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public boolean updateStatus(String maizeCode, String status) {
        MaizeRecord maizeRecord = new MaizeRecord();
        maizeRecord.setMaizeCode(maizeCode);
        maizeRecord.setStatus(status);
        maizeRecord.setUpdateTime(System.currentTimeMillis());
        return super.updateRow(maizeRecord, List.of("status", "update_time"));
    }

    @Override
    public MaizeRecord findMaizeRecord(String maizeCode) {
        MaizeRecord maizeRecord = new MaizeRecord();
        maizeRecord.setMaizeCode(maizeCode);
        return super.getRow(maizeRecord);
    }

    @Override
    public MaizeRecord findMaizeRecord(String customerId, String socialEvent, String socialContentId) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("social_content_id", socialContentId))
                .must(QueryBuilders.term("social_event", socialEvent))
                .must(QueryBuilders.term("customer_id", customerId));

        return searchOne(boolQuery.build(), MaizeRecord.SEARCH_MAIZE_RECORD);
    }

    @Override
    public List<MaizeRecord> findMaizeRecords(String customerId, String socialEvent, String socialContentId) {
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("social_content_id", socialContentId))
                .must(QueryBuilders.term("social_event", socialEvent))
                .must(QueryBuilders.term("customer_id", customerId));

        return search(boolQuery.build(), null, 0, 100, MaizeRecord.SEARCH_MAIZE_RECORD);
    }

    @Override
    public MaizeRecord findMaizeRecordByCompositeKey(String customerId, String socialEvent,
                                                   String socialContentId, String sessionId, Integer progress) {
            BoolQuery.Builder boolQuery = QueryBuilders.bool()
                    .must(QueryBuilders.term("social_content_id", socialContentId))
                    .must(QueryBuilders.term("social_event", socialEvent))
                    .must(QueryBuilders.term("customer_id", customerId))
                    .must(QueryBuilders.term("session_id", sessionId))
                    .must(QueryBuilders.term("progress", progress));
            return searchOne(boolQuery.build(), MaizeRecord.SEARCH_MAIZE_RECORD);
    }

    @Override
    public MaizeRecord findPreviousStageReward(String customerId, String socialEvent,
                                             String socialContentId, String sessionId, Integer currentProgress) {
        if (currentProgress == null || currentProgress <= 1) {
            return null; // 第一阶段没有前一个阶段
        }

        // 查询当前进度之前的所有奖励记录，按进度倒序排列
        BoolQuery.Builder boolQuery = QueryBuilders.bool()
                .must(QueryBuilders.term("social_content_id", socialContentId))
                .must(QueryBuilders.term("social_event", socialEvent))
                .must(QueryBuilders.term("customer_id", customerId))
                .must(QueryBuilders.term("session_id", sessionId))
                .must(QueryBuilders.range("progress").lessThan(currentProgress));

        // 按进度倒序排列，获取最高的前一个进度
        Sort sort = new Sort(Arrays.asList(new FieldSort("progress", SortOrder.DESC)));
        List<MaizeRecord> records = search(boolQuery.build(), sort, 0, 1, MaizeRecord.SEARCH_MAIZE_RECORD);
        return records.isEmpty() ? null : records.get(0);
    }
}
