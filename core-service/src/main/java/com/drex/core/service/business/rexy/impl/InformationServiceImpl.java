package com.drex.core.service.business.rexy.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.CoreResponseCode;
import com.drex.core.api.response.InformationDTO;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.InformationBuilder;
import com.drex.core.dal.tablestore.model.Information;
import com.drex.core.service.business.rexy.InformationService;
import com.drex.core.service.mapperstruct.InformationMapperStruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class InformationServiceImpl implements InformationService {

    @Resource
    private InformationBuilder informationBuilder;

    @Resource
    private InformationMapperStruct informationMapperStruct;

    @Resource
    private IdUtils idUtils;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final String BANNER_KEY = "banner";

    @Override
    public InformationDTO getById(String id) throws CoreException {
        Information information = informationBuilder.getById(id);
        if (information == null) {
            throw new CoreException(CoreResponseCode.DATA_NOT_FOUND);
        }
        return informationMapperStruct.toInformationDTO(information);
    }

    @Override
    public Boolean saveInformation(InformationDTO informationDTO) throws CoreException {
        if (informationDTO == null) {
            throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
        }
        if (informationDTO.getId() == null) {
            informationDTO.setId(idUtils.nextId());
        }
        Information information = informationMapperStruct.toInformation(informationDTO);
        Boolean result = informationBuilder.insert(information);
        if (Boolean.FALSE.equals(result)) {
            throw new CoreException(CoreResponseCode.DATA_OPERATE_FAIL);
        }
        redisTemplate.delete(BANNER_KEY);
        return true;
    }

    @Override
    public Boolean updateInformation(InformationDTO informationDTO) throws CoreException {
        if (informationDTO == null || informationDTO.getId() == null) {
            throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
        }

        // 先检查是否存在
        Information existingInfo = informationBuilder.getById(informationDTO.getId());
        if (existingInfo == null) {
            throw new CoreException(CoreResponseCode.DATA_NOT_FOUND);
        }

        Information information = informationMapperStruct.toInformation(informationDTO);
        Boolean result = informationBuilder.update(information);
        if (Boolean.FALSE.equals(result)) {
            throw new CoreException(CoreResponseCode.DATA_OPERATE_FAIL);
        }
        redisTemplate.delete(BANNER_KEY);
        return true;
    }

    @Override
    public Boolean deleteInformation(String id) throws CoreException {
        if (id == null) {
            throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
        }

        // 先检查是否存在
        Information existingInfo = informationBuilder.getById(id);
        if (existingInfo == null) {
            throw new CoreException(CoreResponseCode.DATA_NOT_FOUND);
        }

        Boolean result = informationBuilder.delete(id);
        if (Boolean.FALSE.equals(result)) {
            throw new CoreException(CoreResponseCode.DATA_OPERATE_FAIL);
        }
        redisTemplate.delete(BANNER_KEY);
        return true;
    }

    @Override
    public List<InformationDTO> getByType(String type) throws CoreException {
        if (type == null) {
            throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
        }

        List<Information> informationList = informationBuilder.getByType(type);
        return informationMapperStruct.toInformationDTOList(informationList);
    }

    @Override
    public List<InformationDTO> getByRecommend(Boolean isRecommend, String position) throws CoreException {
        if (isRecommend == null) {
            throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
        }
        String banner = redisTemplate.opsForValue().get(BANNER_KEY);
        if(StringUtils.isNotBlank(banner)){
            return JSON.parseArray(banner, InformationDTO.class);
        }
        List<Information> informationList = informationBuilder.getByRecommend(isRecommend, position);
        List<InformationDTO> informationDTOList = informationMapperStruct.toInformationDTOList(informationList);
        redisTemplate.opsForValue().set(BANNER_KEY, JSON.toJSONString(informationDTOList));
        return informationDTOList;
    }

    @Override
    public List<InformationDTO> getByTypeAndRecommend(String type, Boolean isRecommend) throws CoreException {
        if (type == null || isRecommend == null) {
            throw new CoreException(CoreResponseCode.INVALID_PARAMETER);
        }

        List<Information> informationList = informationBuilder.getByTypeAndRecommend(type, isRecommend);
        return informationMapperStruct.toInformationDTOList(informationList);
    }

    @Override
    public List<InformationDTO> listAll(Integer offset, Integer limit, String position) throws CoreException {
        List<Information> informationList = informationBuilder.listAll(offset, limit, position);
        return informationMapperStruct.toInformationDTOList(informationList);
    }

    @Override
    public InformationDTO getByLinkId(String linkId) {
        Information information = informationBuilder.getByLinkId(linkId);
        log.info("getByLinkId: linkId:{}, information:{}", linkId, information);
        return informationMapperStruct.toInformationDTO(information);
    }
}
