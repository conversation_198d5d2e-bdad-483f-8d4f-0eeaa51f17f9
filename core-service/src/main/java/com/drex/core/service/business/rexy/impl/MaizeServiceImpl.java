package com.drex.core.service.business.rexy.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.CoreResponseCode;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.*;
import com.drex.core.api.response.InformationDTO;
import com.drex.core.api.response.MaizeDTO;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.model.RexyBusinessCode;
import com.drex.core.service.business.rexy.Base62Encoding;
import com.drex.core.service.business.rexy.InformationService;
import com.drex.core.service.business.rexy.MaizeService;
import com.drex.core.service.business.rexy.RexyBasketService;
import com.drex.core.service.mapperstruct.MaizeMapperStruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MaizeServiceImpl implements MaizeService {

    @Resource
    private SocialCheckManager socialCheckManager;
    @Resource
    private MaizeRecordBuilder maizeRecordBuilder;
    @Resource
    private MaizeMapperStruct maizeMapperStruct;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private IdUtils idUtils;
    @Resource
    private RexyBasketService rexyBasketService;
    @Resource
    private InformationService informationService;

    private static final String COLLECT_MAIZE = "collect_maize";

    @Override
    public MaizeDTO generateMaize(GenerateMaizeRequest request) {
        InformationDTO informationDTO = informationService.getByLinkId(request.getSocialEventBody().getSocialContentId());
        boolean check = socialCheckManager.check(request, informationDTO);
        if(!check){
            return null;
        }
        String key = String.format("%s:%s:%s", COLLECT_MAIZE, request.getSocialEventBody().getSocialContentId(), request.getCustomerId());
        MaizeDTO cachedMaizeDTO = JSON.parseObject(String.valueOf(redisTemplate.opsForHash().get(key, String.valueOf(request.getProgress() == null ? 1 : request.getProgress()))), MaizeDTO.class);
        if(cachedMaizeDTO == null){
            String code = generateCode(request.getSocialPlatform());
            cachedMaizeDTO = MaizeDTO.builder()
                    .code(code)
                    .score(Long.parseLong(informationDTO.getRewardAmount()))
                    .build();
            redisTemplate.opsForHash().put(key, String.valueOf(request.getProgress() == null ? 1 : request.getProgress()), JSON.toJSONString(cachedMaizeDTO));
        }
        MaizeDTO maizeDTO = MaizeDTO.builder()
                .socialPlatform(request.getSocialPlatform())
                .socialEvent(request.getSocialEvent())
                .code(String.valueOf(cachedMaizeDTO.getCode()))
                .level(SocialConstant.MaizeLevelEnum.GOLD.name())
                .score(cachedMaizeDTO.getScore())
                .expireTime(request.getExpireTime())
                .progress(request.getProgress() == null ? 1 : request.getProgress())
                .maizeStatus(RexyConstant.MaizeStatus.ISSUED)
                .build();
        MaizeRecord maizeRecord = maizeMapperStruct.toMaizeRecord(maizeDTO, request);
        if(maizeRecordBuilder.save(maizeRecord)){
            return maizeDTO;
        }
        return null;
    }

    @Override
    public List<MaizeDTO> lastMaize(LastMaizeRequest request) {
        List<MaizeDTO> maizeDTOS = new ArrayList<>();
        //查询相应的资源的奖励
        InformationDTO informationDTO = informationService.getByLinkId(request.getContentId());
        if(informationDTO == null){
            return null;
        }
        //构建奖励段
        List<Integer> rewards = buildRewards(informationDTO.getLink(), Integer.parseInt(informationDTO.getRewardAmount()));
        List<SocialConstant.EventEnum> values = Arrays.stream(SocialConstant.EventEnum.values()).filter(event -> event.getPlatform().contains(request.getSocialPlatform())).toList();
        for(SocialConstant.EventEnum eventEnum : values){
            List<MaizeRecord> maizeRecords = maizeRecordBuilder.findMaizeRecords(request.getCustomerId(), eventEnum.name(), request.getContentId());
            Map<Integer, MaizeRecord> maizeRecordsMap = maizeRecords.stream().collect(Collectors.toMap(MaizeRecord::getProgress, Function.identity()));
            for(int i = 1; i <= rewards.size(); i++){
                MaizeRecord maizeRecord = maizeRecordsMap.get(i);
                MaizeDTO.MaizeDTOBuilder maizeDTOBuilder = MaizeDTO.builder()
                        .progress(i)
                        .score((long)rewards.get(i - 1))
                        .maizeStatus(RexyConstant.MaizeStatus.UNCLAIMED);
                if(maizeRecord != null){
                    maizeDTOBuilder.code(maizeRecord.getMaizeCode());
                    maizeDTOBuilder.maizeStatus(RexyConstant.MaizeStatus.ISSUED);
                    if(RexyConstant.MaizeStatus.CLAIMED.name().equals(maizeRecord.getStatus())){
                        maizeDTOBuilder.maizeStatus(RexyConstant.MaizeStatus.CLAIMED);
                    }else if(maizeRecord.getExpireTime() != null && maizeRecord.getExpireTime() < System.currentTimeMillis()){
                        maizeDTOBuilder.maizeStatus(RexyConstant.MaizeStatus.EXPIRED);
                    }
                }
                MaizeDTO maizeDTO = maizeDTOBuilder.build();
                if(maizeDTO.getCode() == null){
                    String code = generateCode(request.getSocialPlatform());
                    maizeDTO.setCode(code);
                    //放到缓存中
                    String key = String.format("%s:%s:%s", COLLECT_MAIZE, request.getContentId(), request.getCustomerId());
                    redisTemplate.opsForHash().put(key, String.valueOf(maizeDTO.getProgress()), JSON.toJSONString(maizeDTO));
                    redisTemplate.expire(key, 1, TimeUnit.DAYS);
                }
                maizeDTOS.add(maizeDTO);
            }
        }
        return maizeDTOS;
    }

    @Override
    public MaizeDTO collectMaize(CollectMaizeRequest request) throws CoreException {
        //当前的奖励
        MaizeRecord maizeRecord = maizeRecordBuilder.findMaizeRecord(request.getMaizeCode());
        if(maizeRecord == null){
            throw new CoreException(CoreResponseCode.REWARD_NOT_FOUND);
        }
        if(maizeRecord.getExpireTime() != null && maizeRecord.getExpireTime() < System.currentTimeMillis()){
            throw new CoreException(CoreResponseCode.REWARD_EXPIRED);
        }
        if(maizeRecord.getStatus() != null && RexyConstant.MaizeStatus.CLAIMED.name().equals(maizeRecord.getStatus())){
            throw new CoreException(CoreResponseCode.REWARD_ALREADY_CLAIMED);
        }
        MaizeDTO maizeDTO = MaizeDTO.builder()
                .socialPlatform(SocialConstant.PlatformEnum.valueOf(maizeRecord.getSocialPlatform()))
                .socialEvent(SocialConstant.EventEnum.valueOf(maizeRecord.getSocialEvent()))
                .code(request.getMaizeCode())
                .level(maizeRecord.getMaizeLevel())
                .score(maizeRecord.getMaizeScore()).build();
        OperateMaizeKernelRequest build = OperateMaizeKernelRequest.builder()
                .customerId(request.getCustomerId())
                .amount(BigDecimal.valueOf(maizeDTO.getScore()))
                .basketType(RexyConstant.RexyBasketsTypeEnum.normal)
                .operateType(RexyConstant.OperateTypeEnum.collect)
                .businessCode(RexyBusinessCode.COLLECT_MAIZE.getCode())
                .businessId(Base62Encoding.decode(request.getMaizeCode()))
                .build();
        rexyBasketService.collectMaizeKernel(build);
        maizeRecordBuilder.updateStatus(maizeRecord.getMaizeCode(), RexyConstant.MaizeStatus.CLAIMED.name());
        return maizeDTO;
    }

    private String generateCode(SocialConstant.PlatformEnum socialPlatformEnum) {
        String id = idUtils.nextId();
        if(SocialConstant.PlatformEnum.X == socialPlatformEnum){
            id = "21"+id;
        }else if(SocialConstant.PlatformEnum.YouTube == socialPlatformEnum){
            id = "22"+id;
        }
        String code = Base62Encoding.encode(id);
        log.info("generateCode id:{},{}", id, code);
        return code;
    }

    private List<Integer> buildRewards(String link, Integer rewardAmount) {
        if(link.contains("x.com") || link.contains("twitter.com")){
            return List.of(rewardAmount);
        }else if(link.contains("youtube.com/shorts")){
            return Collections.nCopies(3, rewardAmount);
        }else if(link.contains("youtube.com")){
            return Collections.nCopies(3, rewardAmount/4);
        }
        return null;
    }
}
