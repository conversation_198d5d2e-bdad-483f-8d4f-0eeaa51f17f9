package com.drex.core.service.business.rexy.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.request.GenerateMaizeRequest;
import com.drex.core.api.response.InformationDTO;
import com.drex.core.service.business.rexy.SocialCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SocialCheckManager {

    List<SocialCheckService> socialCheckServices;

    public SocialCheckManager(List<SocialCheckService> socialCheckServices){
        this.socialCheckServices = socialCheckServices;
    }

    public boolean check(GenerateMaizeRequest request, InformationDTO informationDTO){
        log.info("SocialCheckManager check request:{}", JSON.toJSONString(request));
        SocialCheckService socialCheckService = socialCheckServices.stream().filter(service -> service.getSocialType().equals(request.getSocialPlatform().name())).findFirst().orElseGet(null);
        if(socialCheckService == null){
            return false;
        }
        return socialCheckService.check(request, informationDTO);
    }
}
