package com.drex.core.service.business.video;

import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.response.VideoReportResponse;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.service.cache.model.SessionEvent;
import lombok.Data;

import java.util.List;

/**
 * 实时奖励服务接口
 * 负责实时计算进度和奖励发放
 */
public interface RealtimeRewardService {

    /**
     * 处理实时事件并计算奖励
     *
     * @param customerId 用户ID
     * @param events 事件列表
     * @param session 会话
     * @return 处理结果
     */
    RealtimeProcessingResult processRealtimeEvents(String customerId, VideoViewingSession session,
                                                  List<SessionEvent> events,
                                                  SocialConstant.PlatformEnum socialPlatform,
                                                  SocialConstant.EventEnum socialEvent);

    /**
     * 计算当前进度
     *
     * @param effectiveWatchSeconds 有效观看时长
     * @param videoDurationSeconds 视频总时长
     * @return 进度等级（1、2、3）
     */
    Integer calculateProgress(int effectiveWatchSeconds, int videoDurationSeconds);

    /**
     * 检查是否可以获得奖励
     *
     * @param customerId 用户ID
     * @param sessionId 会话ID
     * @param videoId 视频ID
     * @param progress 进度等级
     * @return 是否可以获得奖励
     */
    boolean canReceiveReward(String customerId, String sessionId, String videoId, Integer progress, String socialEvent);

    /**
     * 实时处理结果
     */
    @Data
    class RealtimeProcessingResult {
        private boolean success;
        private String message;
        private VideoReportResponse.RewardInfo rewardInfo;
        private double riskScore;
        private boolean hasNewReward;
        private int effectiveWatchSeconds;
        private double watchPercentage;

        public RealtimeProcessingResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
    }

    /**
     * 奖励生成结果
     */
    class RewardGenerationResult {
        private boolean success;
        private String message;
        private String rewardCode;
        private Long rewardAmount;
        private String rewardLevel;

        public RewardGenerationResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getRewardCode() { return rewardCode; }
        public void setRewardCode(String rewardCode) { this.rewardCode = rewardCode; }
        public Long getRewardAmount() { return rewardAmount; }
        public void setRewardAmount(Long rewardAmount) { this.rewardAmount = rewardAmount; }
        public String getRewardLevel() { return rewardLevel; }
        public void setRewardLevel(String rewardLevel) { this.rewardLevel = rewardLevel; }
    }
}
