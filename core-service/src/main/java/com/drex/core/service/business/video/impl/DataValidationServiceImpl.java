package com.drex.core.service.business.video.impl;

import com.drex.core.api.request.VideoReportRequest;
import com.drex.core.model.youtube.YouTubeAntiCheatConstant;
import com.drex.core.service.business.video.DataValidationService;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.util.EventDataConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据验证服务实现
 */
@Slf4j
@Service
public class DataValidationServiceImpl implements DataValidationService {

    private static final long MAX_TIME_DIFFERENCE_MS = 5000; // 5秒

    @Autowired
    private EventDataConverter eventDataConverter;

    @Override
    public ValidationResult validateReportRequest(VideoReportRequest request) {
        try {
            // 1. 基础字段验证
            if (request.getSessionId() == null || request.getSessionId().trim().isEmpty()) {
                return new ValidationResult(false, "Session ID is required", "MISSING_SESSION_ID");
            }

            if (request.getCustomerId() == null || request.getCustomerId().trim().isEmpty()) {
                return new ValidationResult(false, "Customer ID is required", "MISSING_CUSTOMER_ID");
            }

//            if (request.getSignature() == null || request.getSignature().trim().isEmpty()) {
//                return new ValidationResult(false, "Signature is required", "MISSING_SIGNATURE");
//            }

            // 3. 设备指纹验证
            if (request.getDeviceFinger() == null || request.getDeviceFinger().trim().isEmpty()) {
                return new ValidationResult(false, "Device finger is required", "MISSING_DEVICE_FINGER");
            }

            // 4. 事件列表验证（如果已解密）
            if (request.getEvents() != null && !request.getEvents().isEmpty()) {
                for (VideoReportRequest.EventData event : request.getEvents()) {
                    if (!isValidEventType(event.getEventType())) {
                        return new ValidationResult(false,
                                "Invalid event type: " + event.getEventType(),
                                "INVALID_EVENT_TYPE");
                    }

                    // 验证新的事件详细数据结构
                    ValidationResult eventDetailsResult = validateEventDetails(event);
                    if (!eventDetailsResult.isValid()) {
                        return eventDetailsResult;
                    }
                }
            }

            return new ValidationResult(true, "Validation passed", "SUCCESS");

        } catch (Exception e) {
            log.error("Failed to validate report request", e);
            return new ValidationResult(false, "Validation error occurred", "VALIDATION_ERROR");
        }
    }

    @Override
    public List<SessionEvent> validateAndDecryptEvents(VideoReportRequest request, String signature, String secretKey) {
        try {
            // 1. 验证签名
//            if (!verifySignature(encryptedData, signature, secretKey)) {
//                log.warn("Invalid signature detected");
//                return new ArrayList<>();
//            }

            // 2. 使用EventDataConverter转换事件数据
            List<SessionEvent> events = eventDataConverter.convertToSessionEvents(
                    request.getEvents(),
                    request.getSessionId(),
                    request.getCustomerId(),
                    request.getSocialPlatform(),
                    request.getSocialEvent(),
                    request.getDeviceFinger(),
                    request.getClientIp()
            );

            // 3. 验证事件格式
            return events.stream()
                    .filter(event -> validateEventFormat(event).isValid())
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Failed to validate and decrypt events", e);
            return new ArrayList<>();
        }
    }

    @Override
    public TimestampValidationResult validateTimestamps(List<SessionEvent> events, Long serverTimestamp) {
        List<Long> anomalousEventIds = new ArrayList<>();
        double maxTimeDifference = 0.0;
        int anomalousCount = 0;

        for (SessionEvent event : events) {
            if (event.getClientTimestamp() != null && event.getServerTimestamp() != null) {
                double timeDiff = Math.abs(event.getServerTimestamp() - event.getClientTimestamp());
                maxTimeDifference = Math.max(maxTimeDifference, timeDiff);

                if (timeDiff > MAX_TIME_DIFFERENCE_MS) {
                    anomalousEventIds.add(event.getClientTimestamp());
                    anomalousCount++;
                }
            }
        }

        boolean isValid = anomalousCount == 0;
        return new TimestampValidationResult(isValid, maxTimeDifference, anomalousCount, anomalousEventIds);
    }

    @Override
    public FormatValidationResult validateEventFormat(SessionEvent event) {
        List<String> formatErrors = new ArrayList<>();

        if (event.getEventType() == null || event.getEventType().trim().isEmpty()) {
            formatErrors.add("Event type is required");
        } else if (!isValidEventType(event.getEventType())) {
            formatErrors.add("Invalid event type: " + event.getEventType());
        }

        if (event.getClientTimestamp() == null) {
            formatErrors.add("Client timestamp is required");
        }

        if (event.getSessionId() == null || event.getSessionId().trim().isEmpty()) {
            formatErrors.add("Session ID is required");
        }

        if (event.getCustomerId() == null || event.getCustomerId().trim().isEmpty()) {
            formatErrors.add("Customer ID is required");
        }

        // 验证事件数据格式
        if (event.getEventData() != null) {
            validateEventDataFormat(event.getEventType(), event.getEventData(), formatErrors);
        }

        boolean isValid = formatErrors.isEmpty();
        return new FormatValidationResult(isValid, formatErrors);
    }


    /**
     * 检查事件类型是否有效
     * 使用YouTubeAntiCheatConstant.EventType枚举进行验证
     */
    private boolean isValidEventType(String eventType) {
        if (eventType == null || eventType.trim().isEmpty()) {
            return false;
        }

        try {
            // 遍历所有枚举值，检查是否匹配
            for (YouTubeAntiCheatConstant.EventType type : YouTubeAntiCheatConstant.EventType.values()) {
                if (type.getCode().equals(eventType)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.warn("Failed to validate event type: {}", eventType, e);
            return false;
        }
    }

    /**
     * 验证事件详细数据结构
     */
    private ValidationResult validateEventDetails(VideoReportRequest.EventData event) {
        try {
            if (event.getDetails() == null) {
                return new ValidationResult(true, "Using legacy event data format", "SUCCESS");
            }

            String eventType = event.getEventType();
            VideoReportRequest.EventDetails details = event.getDetails();

            switch (eventType) {
                case "PLAYING":
                    return validatePlayingEventData(details.getPlayingData());
                case "PAUSED":
                    return validatePausedEventData(details.getPausedData());
                case "SEEK":
                    return validateSeekEventData(details.getSeekData());
                case "FOCUS_LOST":
                    return validateFocusLostEventData(details.getFocusLostData());
                case "FOCUS_GAINED":
                    return validateFocusGainedEventData(details.getFocusGainedData());
                case "USER_STATE":
                    return validateUserStateEventData(details.getUserStateData());
                default:
                    // 对于其他事件类型，不强制要求details
                    return new ValidationResult(true, "Event type does not require specific details validation", "SUCCESS");
            }

        } catch (Exception e) {
            log.error("Failed to validate event details for event type: {}", event.getEventType(), e);
            return new ValidationResult(false, "Event details validation failed: " + e.getMessage(), "VALIDATION_ERROR");
        }
    }

    /**
     * 验证PLAYING事件数据
     */
    private ValidationResult validatePlayingEventData(VideoReportRequest.PlayingEventData data) {
        if (data == null) {
            return new ValidationResult(false, "Playing event data is required", "MISSING_PLAYING_DATA");
        }
        if (data.getNewState() == null || data.getNewState().trim().isEmpty()) {
            return new ValidationResult(false, "New state is required for playing event", "MISSING_NEW_STATE");
        }
        if (data.getCurrentTime() == null || data.getCurrentTime() < 0) {
            return new ValidationResult(false, "Valid current time is required for playing event", "INVALID_CURRENT_TIME");
        }
        if (data.getPlaybackRate() == null || data.getPlaybackRate() <= 0) {
            return new ValidationResult(false, "Valid playback rate is required for playing event", "INVALID_PLAYBACK_RATE");
        }
        return new ValidationResult(true, "Playing event data is valid", "SUCCESS");
    }

    /**
     * 验证PAUSED事件数据
     */
    private ValidationResult validatePausedEventData(VideoReportRequest.PausedEventData data) {
        if (data == null) {
            return new ValidationResult(false, "Paused event data is required", "MISSING_PAUSED_DATA");
        }
        if (data.getNewState() == null || data.getNewState().trim().isEmpty()) {
            return new ValidationResult(false, "New state is required for paused event", "MISSING_NEW_STATE");
        }
        if (data.getCurrentTime() == null || data.getCurrentTime() < 0) {
            return new ValidationResult(false, "Valid current time is required for paused event", "INVALID_CURRENT_TIME");
        }
        return new ValidationResult(true, "Paused event data is valid", "SUCCESS");
    }

    /**
     * 验证SEEK事件数据
     */
    private ValidationResult validateSeekEventData(VideoReportRequest.SeekEventData data) {
        if (data == null) {
            return new ValidationResult(false, "Seek event data is required", "MISSING_SEEK_DATA");
        }
        if (data.getCurrentTime() == null || data.getCurrentTime() < 0) {
            return new ValidationResult(false, "Valid current time is required for seek event", "INVALID_CURRENT_TIME");
        }
        if (data.getPreviousTime() == null || data.getPreviousTime() < 0) {
            return new ValidationResult(false, "Valid previous time is required for seek event", "INVALID_PREVIOUS_TIME");
        }
        return new ValidationResult(true, "Seek event data is valid", "SUCCESS");
    }

    /**
     * 验证FOCUS_LOST事件数据
     */
    private ValidationResult validateFocusLostEventData(VideoReportRequest.FocusLostEventData data) {
        if (data == null) {
            return new ValidationResult(false, "Focus lost event data is required", "MISSING_FOCUS_LOST_DATA");
        }
        if (data.getTabActive() == null) {
            return new ValidationResult(false, "Tab active status is required for focus lost event", "MISSING_TAB_ACTIVE");
        }
        if (data.getWindowFocused() == null) {
            return new ValidationResult(false, "Window focused status is required for focus lost event", "MISSING_WINDOW_FOCUSED");
        }
        return new ValidationResult(true, "Focus lost event data is valid", "SUCCESS");
    }

    /**
     * 验证FOCUS_GAINED事件数据
     */
    private ValidationResult validateFocusGainedEventData(VideoReportRequest.FocusGainedEventData data) {
        if (data == null) {
            return new ValidationResult(false, "Focus gained event data is required", "MISSING_FOCUS_GAINED_DATA");
        }
        if (data.getTabActive() == null) {
            return new ValidationResult(false, "Tab active status is required for focus gained event", "MISSING_TAB_ACTIVE");
        }
        if (data.getWindowFocused() == null) {
            return new ValidationResult(false, "Window focused status is required for focus gained event", "MISSING_WINDOW_FOCUSED");
        }
        return new ValidationResult(true, "Focus gained event data is valid", "SUCCESS");
    }

    /**
     * 验证USER_STATE事件数据
     */
    private ValidationResult validateUserStateEventData(VideoReportRequest.UserStateEventData data) {
        if (data == null) {
            return new ValidationResult(false, "User state event data is required", "MISSING_USER_STATE_DATA");
        }
        if (data.getState() == null || data.getState().trim().isEmpty()) {
            return new ValidationResult(false, "State is required for user state event", "MISSING_STATE");
        }
        // 验证状态值是否有效
        String state = data.getState().toUpperCase();
        if (!Arrays.asList("ACTIVE", "IDLE", "LOCKED").contains(state)) {
            return new ValidationResult(false, "Invalid user state: " + data.getState(), "INVALID_USER_STATE");
        }
        return new ValidationResult(true, "User state event data is valid", "SUCCESS");
    }

    /**
     * 验证事件数据格式
     */
    private void validateEventDataFormat(String eventType, Map<String, Object> eventData, 
                                       List<String> formatErrors) {
        // TODO: 根据不同的事件类型验证数据格式
        // 例如：TIMEUPDATE事件应该包含currentTime字段
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
}
