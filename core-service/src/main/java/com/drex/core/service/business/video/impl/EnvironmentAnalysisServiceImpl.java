package com.drex.core.service.business.video.impl;

import com.alibaba.fastjson.JSON;
import com.drex.core.api.request.VideoSessionInitRequest;
import com.drex.core.dal.tablestore.builder.IpReputationBuilder;
import com.drex.core.dal.tablestore.builder.UserFingerprintBuilder;
import com.drex.core.dal.tablestore.model.IpReputation;
import com.drex.core.dal.tablestore.model.UserFingerprint;
import com.drex.core.service.business.video.EnvironmentAnalysisService;
import com.drex.core.service.business.video.model.VideoIndicatorConfig;
import com.drex.core.service.config.VideoRiskProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 环境分析服务实现
 * 基于提供的欺诈指标阈值设计实现
 */
@Slf4j
@Service
public class EnvironmentAnalysisServiceImpl implements EnvironmentAnalysisService {

    @Resource
    private UserFingerprintBuilder userFingerprintBuilder;

    @Resource
    private IpReputationBuilder ipReputationBuilder;

    @Resource
    private VideoRiskProperties videoRiskProperties;

    @Override
    public double analyzeBrowserFingerprint(String fingerprint, String customerId, String ipAddress) {
        try {
            if (StringUtils.isBlank(fingerprint)) {
                return 1.0;
            }

            // 查找现有指纹
            UserFingerprint existingFingerprint = userFingerprintBuilder.getByFingerprintHash(fingerprint);
            boolean isNewFingerprint = existingFingerprint == null;

            int associatedUserCount = 0;
            boolean isSuspicious = false;

            if (!isNewFingerprint) {
                // 计算关联的用户数量
                if (existingFingerprint.getAssociatedUserIds() != null) {
                    String[] userIds = existingFingerprint.getAssociatedUserIds().split(",");
                    associatedUserCount = userIds.length;
                }
            } else {
                VideoSessionInitRequest.BrowserFingerprint userFingerprint = new VideoSessionInitRequest.BrowserFingerprint();
                userFingerprint.setFingerprintHash(fingerprint);
                updateUserFingerprint(customerId, userFingerprint, ipAddress);
            }

            return calculateLinearScoreWithMax(
                    associatedUserCount,
                    videoRiskProperties.getVideoIndicatorConfig().getFingerprintDuplication().getAccountsThreshold(),
                    videoRiskProperties.getVideoIndicatorConfig().getFingerprintDuplication().getAccountsMaxAllowed()
            );
        } catch (Exception e) {
            log.error("Failed to analyze browser fingerprint", e);
            return 0.0;
        }
    }

    @Override
    public IpReputationAnalysisResult analyzeIpReputation(String ipAddress) {
        try {
            if (ipAddress == null || ipAddress.trim().isEmpty()) {
                return new IpReputationAnalysisResult(false, 100, "UNKNOWN");
            }

            // 查找IP信誉信息
            IpReputation ipReputation = ipReputationBuilder.getByIpAddress(ipAddress);

            boolean isMalicious = false;
            boolean isProxy = false;
            boolean isVpn = false;
            boolean isTorExitNode = false;
            boolean isDataCenter = false;
            int reputationScore = 100; // 默认高信誉
            String countryCode = "UNKNOWN";
            List<String> riskIndicators = new ArrayList<>();

            if (ipReputation != null) {
                isProxy = Boolean.TRUE.equals(ipReputation.getIsProxy());
                isVpn = Boolean.TRUE.equals(ipReputation.getIsVpn());
                isTorExitNode = Boolean.TRUE.equals(ipReputation.getIsTorExitNode());
                isDataCenter = Boolean.TRUE.equals(ipReputation.getIsDataCenter());
                reputationScore = ipReputation.getReputationScore() != null ?
                        ipReputation.getReputationScore() : 100;
                countryCode = ipReputation.getCountryCode() != null ?
                        ipReputation.getCountryCode() : "UNKNOWN";

                // 检测恶意IP
                if (isProxy) {
                    isMalicious = true;
                    riskIndicators.add("PROXY_IP");
                }
                if (isVpn) {
                    isMalicious = true;
                    riskIndicators.add("VPN_IP");
                }
                if (isTorExitNode) {
                    isMalicious = true;
                    riskIndicators.add("TOR_EXIT_NODE");
                }
                if (isDataCenter) {
                    isMalicious = true;
                    riskIndicators.add("DATA_CENTER_IP");
                }
                if (reputationScore < 50) {
                    isMalicious = true;
                    riskIndicators.add("LOW_REPUTATION_SCORE");
                }
            } else {
                // 新IP，需要进行信誉检查
                log.info("New IP detected, reputation check needed: {}", ipAddress);
                // 更新入库
                IpReputationUpdateResult updateResult = updateIpReputation(ipAddress, new HashMap<>());

            }
            IpReputationAnalysisResult result = new IpReputationAnalysisResult(
                    isMalicious, reputationScore, countryCode);
            result.setProxy(isProxy);
            result.setVpn(isVpn);
            result.setTorExitNode(isTorExitNode);
            result.setDataCenter(isDataCenter);
            result.setIpReputation(ipReputation);
            result.setRiskIndicators(riskIndicators);

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze IP reputation for: {}", ipAddress, e);
            return new IpReputationAnalysisResult(true, 0, "UNKNOWN");
        }
    }

    @Override
    public double analyzeEnvironmentConsistency(String customerId, String currentFingerprint, String currentIp) {
        try {
            // 获取用户历史指纹
            List<UserFingerprint> userFingerprints = userFingerprintBuilder.getByCustomerId(customerId);

            // 检查指纹一致性
            int fingerprintChangeCount = fingerprintChangeCount(userFingerprints, currentFingerprint);

            // 检查IP变化频率
            int ipChangeCount = ipChangeCount(customerId, currentIp);

            return calculateConsistencyScore(fingerprintChangeCount, ipChangeCount);
        } catch (Exception e) {
            log.error("Failed to analyze environment consistency for user: {}", customerId, e);
            return 0.0;
        }
    }

    /**
     * 检查指纹一致性
     * @param userFingerprints 用户历史指纹记录
     * @param currentFingerprint 当前指纹
     * @return 历史指纹变更次数
     */
    private int fingerprintChangeCount(List<UserFingerprint> userFingerprints, String currentFingerprint) {
        if (userFingerprints == null || userFingerprints.isEmpty()) {
            return 0; // 没有历史记录
        }

        // 计算不同的指纹数量
        long distinctFingerprintCount = userFingerprints.stream()
            .map(UserFingerprint::getFingerprintHash)
            .distinct()
            .count();

        // 如果当前指纹与最近使用的不一致，则记录原因
        String latestFingerprintHash = userFingerprints.get(0).getFingerprintHash();
        if (!currentFingerprint.equals(latestFingerprintHash)) {
            // 检查是否是已知的指纹变更
            boolean isKnownChange = userFingerprints.stream()
                .skip(1) // 跳过最新的，因为已经知道它不同
                .anyMatch(fp -> currentFingerprint.equals(fp.getFingerprintHash()));

            if (!isKnownChange) {
                distinctFingerprintCount++; // 新增一个不同的指纹
            }
        }
        return (int) distinctFingerprintCount;
    }

    /**
     * 检查IP一致性
     * @param customerId 用户ID
     * @param currentIp 当前IP
     * @return 历史IP变更次数
     */
    private int ipChangeCount(String customerId, String currentIp) {

        // 获取用户一个小时内的指纹
        List<UserFingerprint> userFingerprints = userFingerprintBuilder.getByTimeRange(customerId ,System.currentTimeMillis() - 3600 * 1000, System.currentTimeMillis());
        if (userFingerprints == null || userFingerprints.isEmpty()) {
            return 0; // 没有历史记录
        }

        // 计算不同的IP数量
        long distinctIpCount = userFingerprints.stream()
            .map(UserFingerprint::getIpAddress)
            .filter(ip -> ip != null && !ip.isEmpty())
            .distinct()
            .count();

        // 获取最近使用的IP
        String latestIp = userFingerprints.get(0).getIpAddress();
        
        // 如果IP发生变化
        if (!currentIp.equals(latestIp)) {
            // 检查是否是已知的IP变更
            boolean isKnownIp = userFingerprints.stream()
                .skip(1) // 跳过最新的，因为已经知道它不同
                .anyMatch(fp -> currentIp.equals(fp.getIpAddress()));

            if (!isKnownIp) {
                distinctIpCount++; // 新增一个不同的IP
            }
        }
        
        return (int) distinctIpCount;
    }
    
    /**
      * 计算一致性分数
      */
    private double calculateConsistencyScore(int fingerprintChangeCount, int ipChangeCount) {
        VideoIndicatorConfig.EnvironmentConfig environment = videoRiskProperties.getVideoIndicatorConfig().getEnvironment();

        double fingerChangeScore = 0.0;
        double ipChangeScore = 0.0;

        // 指纹变化
        if (fingerprintChangeCount < environment.getAccountsThreshold()) {
            fingerChangeScore = 0.0;
        } else if (fingerprintChangeCount > environment.getIpChangesMaxAllowed()) {
            fingerChangeScore = 1.0;
        } else {
            fingerChangeScore = (fingerprintChangeCount - environment.getAccountsThreshold()) / (environment.getIpChangesMaxAllowed() - environment.getAccountsThreshold());
        }
        fingerChangeScore = Math.max(0.0, Math.min(fingerChangeScore, 1.0));


        // IP变化
        if (ipChangeCount < environment.getIpChangesThreshold()) {
            ipChangeScore = 0.0;
        } else if (ipChangeCount > environment.getIpChangesMaxAllowed()) {
            ipChangeScore = 1.0;
        } else {
            ipChangeScore = (ipChangeCount - environment.getIpChangesThreshold()) / (environment.getIpChangesMaxAllowed() - environment.getIpChangesThreshold());
        }
        ipChangeScore = Math.max(0.0, Math.min(ipChangeScore, 1.0));

        return Math.max(fingerChangeScore, ipChangeScore);
    }


    @Override
    public FingerprintUpdateResult updateUserFingerprint(String customerId,
                                                        VideoSessionInitRequest.BrowserFingerprint fingerprint,
                                                        String ipAddress) {
        try {
            if (fingerprint == null || fingerprint.getFingerprintHash() == null) {
                return new FingerprintUpdateResult(false, false, null);
            }

            String fingerprintHash = fingerprint.getFingerprintHash();
            UserFingerprint existingFingerprint = userFingerprintBuilder.getByFingerprintHash(fingerprintHash);

            boolean isNewFingerprint = existingFingerprint == null;
            String fingerprintId;

            if (isNewFingerprint) {
                // 创建新指纹记录
                UserFingerprint newFingerprint = new UserFingerprint();
                newFingerprint.setCustomerId(customerId);
                newFingerprint.setFingerprintHash(fingerprintHash);
                newFingerprint.setAttributes(serializeFingerprintAttributes(fingerprint));
                newFingerprint.setIpAddress(ipAddress);
                newFingerprint.setAssociatedUserIds(customerId);
                newFingerprint.setIsSuspicious(false);

                boolean success = userFingerprintBuilder.insert(newFingerprint);
                fingerprintId = success ? newFingerprint.getId() : null;

                return new FingerprintUpdateResult(success, true, fingerprintId);
            } else {
                // 更新现有指纹记录
                updateAssociatedUsers(existingFingerprint, customerId);
                existingFingerprint.setIpAddress(ipAddress);

                boolean success = userFingerprintBuilder.update(existingFingerprint);
                fingerprintId = existingFingerprint.getId();

                return new FingerprintUpdateResult(success, false, fingerprintId);
            }

        } catch (Exception e) {
            log.error("Failed to update user fingerprint for user: {}", customerId, e);
            FingerprintUpdateResult result = new FingerprintUpdateResult(false, false, null);
            result.setErrorMessage("Failed to update fingerprint: " + e.getMessage());
            return result;
        }
    }

    @Override
    public IpReputationUpdateResult updateIpReputation(String ipAddress, Map<String, Object> reputationData) {
        try {
            IpReputation existingReputation = ipReputationBuilder.getByIpAddress(ipAddress);
            boolean isNewIp = existingReputation == null;

            if (isNewIp) {
                // 创建新IP信誉记录
                IpReputation newReputation = new IpReputation();
                newReputation.setIpAddress(ipAddress);
                populateIpReputationFromData(newReputation, reputationData);

                boolean success = ipReputationBuilder.insert(newReputation);
                return new IpReputationUpdateResult(success, true);
            } else {
                // 更新现有IP信誉记录
                populateIpReputationFromData(existingReputation, reputationData);

                boolean success = ipReputationBuilder.update(existingReputation);
                return new IpReputationUpdateResult(success, false);
            }

        } catch (Exception e) {
            log.error("Failed to update IP reputation for: {}", ipAddress, e);
            IpReputationUpdateResult result = new IpReputationUpdateResult(false, false);
            result.setErrorMessage("Failed to update IP reputation: " + e.getMessage());
            return result;
        }
    }

    /**
     * 计算线性分数，用于将指标值映射到0-1范围
     * @param value 当前值
     * @param threshold 阈值，低于此值得分为0
     * @param maxAllowed 最大值，高于此值得分为1
     * @return 0-1之间的分数
     */
    public double calculateLinearScoreWithMax(double value, double threshold, double maxAllowed) {
        if (value <= threshold) {
            return 0.0;
        } else if (value >= maxAllowed) {
            return 1.0;
        } else {
            return (value - threshold) / (maxAllowed - threshold);
        }
    }

    private String serializeFingerprintAttributes(VideoSessionInitRequest.BrowserFingerprint fingerprint) {
        return JSON.toJSONString(fingerprint); // 简化实现，应该序列化为JSON
    }

    private void updateAssociatedUsers(UserFingerprint fingerprint, String customerId) {
        String currentUsers = fingerprint.getAssociatedUserIds();
        if (currentUsers == null || !currentUsers.contains(customerId)) {
            String newUsers = currentUsers == null ? customerId : currentUsers + "," + customerId;
            fingerprint.setAssociatedUserIds(newUsers);
        }
    }

    private void populateIpReputationFromData(IpReputation reputation, Map<String, Object> data) {
        // 简化实现，应该从外部数据源填充IP信誉信息
        reputation.setReputationScore(100);
        reputation.setIsProxy(false);
        reputation.setIsVpn(false);
        reputation.setIsTorExitNode(false);
        reputation.setIsDataCenter(false);
        reputation.setCountryCode("UNKNOWN");
        reputation.setSource("INTERNAL");
    }
}
