package com.drex.core.service.business.video.impl;

import com.drex.core.service.business.video.model.FraudIndexResult;
import com.drex.core.service.business.video.model.RiskIndicators;
import com.drex.core.service.business.video.model.VideoIndicatorConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * 欺诈指数计算服务实现
 */
@Slf4j
public class FraudScoreCalculator {

    public static FraudIndexResult calculateFraudScore(RiskIndicators indicators, VideoIndicatorConfig videoIndicatorConfig) {
        try {
            double rawScore = calculateRawScore(indicators, videoIndicatorConfig);
            double normalizedScore = normalizeScore(rawScore);
            double finalFraudScore = applyScoreAdjustments(normalizedScore);

            FraudIndexResult result = new FraudIndexResult(rawScore, normalizedScore, finalFraudScore);
            log.debug("Fraud score calculated: result = {}", result);
            return result;
        } catch (Exception e) {
            log.error("Failed to calculate Fraud score", e);
            return new FraudIndexResult(0.0, 0.0, 0.0);
        }
    }

    /**p
     * 计算原始分数
     * 基于提供的欺诈指标阈值设计实现
     * 公式：TrustScore = Σ(IndicatorValue × Weight)
     */
    private static double calculateRawScore(RiskIndicators indicators, VideoIndicatorConfig videoIndicatorConfig) {
        double weightedScore = 0.0;
        double totalWeight = 0.0;

        // 计算每个指标的加权分数
        if (indicators.getRepeatedEvents() > 0) {
            weightedScore += indicators.getRepeatedEvents() * videoIndicatorConfig.getRepeatedEvent().getWeight();
            totalWeight += videoIndicatorConfig.getRepeatedEvent().getWeight();
        }
        if (indicators.getAbnormalCompletion() > 0) {
            weightedScore += indicators.getAbnormalCompletion() * videoIndicatorConfig.getCompletion().getWeight();
            totalWeight += videoIndicatorConfig.getCompletion().getWeight();
        }
        if (indicators.getLowFocusDuration() > 0) {
            weightedScore += indicators.getLowFocusDuration() * videoIndicatorConfig.getFocus().getWeight();
            totalWeight += videoIndicatorConfig.getFocus().getWeight();
        }
        if (indicators.getLongIdleDuration() > 0) {
            weightedScore += indicators.getLongIdleDuration() * videoIndicatorConfig.getIdle().getWeight();
            totalWeight += videoIndicatorConfig.getIdle().getWeight();
        }
        if (indicators.getEnvironmentInconsistency() > 0) {
            weightedScore += indicators.getEnvironmentInconsistency() * videoIndicatorConfig.getEnvironment().getWeight();
            totalWeight += videoIndicatorConfig.getEnvironment().getWeight();
        }
        if (indicators.getTimestampAnomaly() > 0) {
            weightedScore += indicators.getTimestampAnomaly() * videoIndicatorConfig.getTimestamp().getWeight();
            totalWeight += videoIndicatorConfig.getTimestamp().getWeight();
        }
        if (indicators.getEventOrderAnomaly() > 0) {
            weightedScore += indicators.getEventOrderAnomaly() * videoIndicatorConfig.getEventOrder().getWeight();
            totalWeight += videoIndicatorConfig.getEventOrder().getWeight();
        }
        if (indicators.getExcessivePlaybackSpeed() > 0) {
            weightedScore += indicators.getExcessivePlaybackSpeed() * videoIndicatorConfig.getPlaybackSpeed().getWeight();
            totalWeight += videoIndicatorConfig.getPlaybackSpeed().getWeight();
        }
        if (indicators.getAbnormalSeek() > 0) {
            weightedScore += indicators.getAbnormalSeek() * videoIndicatorConfig.getAbnormalSeek().getWeight();
            totalWeight += videoIndicatorConfig.getAbnormalSeek().getWeight();
        }
        if (indicators.getFingerprintDuplication() > 0) {
            weightedScore += indicators.getFingerprintDuplication() * videoIndicatorConfig.getFingerprintDuplication().getWeight();
            totalWeight += videoIndicatorConfig.getFingerprintDuplication().getWeight();
        }
        if (indicators.getMaliciousIp() > 0) {
            weightedScore += indicators.getMaliciousIp() * videoIndicatorConfig.getMaliciousIp().getWeight();
            totalWeight += videoIndicatorConfig.getMaliciousIp().getWeight();
        }
        // 返回加权平均分数
        return totalWeight > 0 ? weightedScore / totalWeight : 0.0;
    }

    /**
     * 标准化分数到0-1范围
     */
    private static double normalizeScore(double rawScore) {
        return Math.max(0.0, Math.min(1.0, rawScore));
    }

    /**
     * 应用分数调整
     */
    private static double applyScoreAdjustments(double normalizedScore) {
        // 转化为百分制
        return Math.max(0.0, Math.min(1.0, normalizedScore)) * 100;
    }

}
