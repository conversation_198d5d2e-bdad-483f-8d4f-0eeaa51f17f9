package com.drex.core.service.business.video.impl;

import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.response.VideoReportResponse;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.service.business.video.RealtimeRewardService;
import com.drex.core.service.business.video.model.FraudIndexResult;
import com.drex.core.service.business.video.worker.EventProcessingWorker;
import com.drex.core.service.business.video.worker.FraudCalculationWorker;
import com.drex.core.service.business.video.worker.RewardCalculationWorker;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.util.async.executor.Async;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实时奖励服务实现
 */
@Slf4j
@Service
public class RealtimeRewardServiceImpl implements RealtimeRewardService {

    @Autowired
    private MaizeRecordBuilder maizeRecordBuilder;

    @Autowired
    private EventProcessingWorker eventProcessingWorker;

    @Autowired
    private FraudCalculationWorker fraudCalculationWorker;

    @Autowired
    private RewardCalculationWorker rewardCalculationWorker;

    @Override
    public RealtimeProcessingResult processRealtimeEvents(String customerId, VideoViewingSession session,
                                                          List<SessionEvent> events, SocialConstant.PlatformEnum socialPlatform, SocialConstant.EventEnum socialEvent) {
        String sessionId = session.getSessionId();
        try {
            log.debug("Processing realtime events for session: {}, events: {}", session.getSessionId(), events.size());

            // 使用WorkerWrapper模式编排异步处理流程
            long startTime = System.currentTimeMillis();

            // 创建事件处理Worker
            WorkerWrapper<EventProcessingWorker.EventProcessingParam, EventProcessingWorker.EventProcessingResult> eventWorkerWrapper =
                    new WorkerWrapper.Builder<EventProcessingWorker.EventProcessingParam, EventProcessingWorker.EventProcessingResult>()
                            .worker(eventProcessingWorker)
                            .callback(eventProcessingWorker)
                            .param(new EventProcessingWorker.EventProcessingParam(sessionId, events, session.getVideoUrl(), session.getVideoDurationSeconds()))
                            .build();

            // 创建风险分数计算Worker（并行执行）
            WorkerWrapper<VideoViewingSession, FraudIndexResult> riskScoreWorkerWrapper =
                    new WorkerWrapper.Builder<VideoViewingSession, FraudIndexResult>()
                            .worker(fraudCalculationWorker)
                            .callback(fraudCalculationWorker)
                            .param(session)
                            .build();

            // 创建奖励计算Worker（依赖前面的结果）
            WorkerWrapper<RewardCalculationWorker.RewardCalculationParam, RewardGenerationResult> rewardWorkerWrapper =
                    new WorkerWrapper.Builder<RewardCalculationWorker.RewardCalculationParam, RewardGenerationResult>()
                            .worker(rewardCalculationWorker)
                            .callback(rewardCalculationWorker)
                            .depend(eventWorkerWrapper, riskScoreWorkerWrapper)
                            .param(new RewardCalculationWorker.RewardCalculationParam(
                                    sessionId, customerId, session.getVideoId(), session.getVideoUrl(), socialPlatform, socialEvent, session.getVideoDurationSeconds()))
                            .build();

            // 开始执行并行任务
            log.debug("Starting parallel execution for session: {}", sessionId);
            Async.beginWork(3000, eventWorkerWrapper, riskScoreWorkerWrapper); // 3秒超时

            RealtimeProcessingResult result = new RealtimeProcessingResult(true, "Processing completed");

            RewardGenerationResult rewardResult = rewardWorkerWrapper.getWorkResult().getResult();
            EventProcessingWorker.EventProcessingResult eventProcessingResult = eventWorkerWrapper.getWorkResult().getResult();
            FraudIndexResult fraudIndexResult = riskScoreWorkerWrapper.getWorkResult().getResult();
            result.setRiskScore(fraudIndexResult.getFinalFraudScore());

            if (rewardResult != null && rewardResult.isSuccess()) {
                VideoReportResponse.RewardInfo rewardInfo = VideoReportResponse.RewardInfo.builder()
                        .rewardCode(rewardResult.getRewardCode())
                        .rewardAmount(rewardResult.getRewardAmount())
                        .rewardLevel(rewardResult.getRewardLevel())
                        .progress(eventProcessingResult.getCurrentProgress())
                        .build();

                result.setRewardInfo(rewardInfo);
                result.setHasNewReward(true);
                log.info("Generated reward for session: {}, progress: {}, code: {}, riskScore: {}",
                        sessionId, eventProcessingResult.getCurrentProgress(), rewardResult.getRewardCode(), fraudIndexResult.getFinalFraudScore());
            } else {
                log.warn("Reward generation failed for session: {}, progress: {}",
                        sessionId, eventProcessingResult.getCurrentProgress());
            }


            log.debug("Realtime processing completed for session: {}, total cost: {}ms",
                    sessionId, System.currentTimeMillis() - startTime);

            result.setEffectiveWatchSeconds(eventProcessingResult.getEffectiveWatchSeconds());
            result.setWatchPercentage(eventProcessingResult.getWatchPercentage());
            return result;

        } catch (Exception e) {
            log.error("Failed to process realtime events for session: {}", sessionId, e);
            return new RealtimeProcessingResult(false, "Processing failed: " + e.getMessage());
        }
    }



    @Override
    public Integer calculateProgress(int effectiveWatchSeconds, int videoDurationSeconds) {
        if (videoDurationSeconds <= 0) {
            return null;
        }

        double watchPercentage = (double) effectiveWatchSeconds / videoDurationSeconds;
        
        // 根据有效播放时长占视频总时长的百分比计算progress进度
        if (watchPercentage >= 0.2 && watchPercentage <= 0.4) {
            return 1;
        } else if (watchPercentage >= 0.5 && watchPercentage <= 0.7) {
            return 2;
        } else if (watchPercentage >= 0.8 && watchPercentage <= 1.2) {
            return 3;
        }
        
        return null;
    }

    @Override
    public boolean canReceiveReward(String customerId, String sessionId, String videoId, Integer progress, String socialEvent) {
        try {
            // 使用组合键查询maizeRecord表检查是否已发放奖励
            // customerId + socialEvent + socialPlatform + socialContentId + sessionId + progress
            MaizeRecord existingRecord = maizeRecordBuilder.findMaizeRecordByCompositeKey(
                    customerId, socialEvent, videoId, sessionId, progress);
            
            return existingRecord == null;
            
        } catch (Exception e) {
            log.error("Failed to check reward eligibility for session: {}, progress: {}", sessionId, progress, e);
            return false;
        }
    }
}
