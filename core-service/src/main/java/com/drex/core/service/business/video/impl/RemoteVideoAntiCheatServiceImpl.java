package com.drex.core.service.business.video.impl;

import com.drex.core.api.RemoteVideoAntiCheatService;
import com.drex.core.api.request.VideoReportRequest;
import com.drex.core.api.request.VideoSessionInitRequest;
import com.drex.core.api.response.VideoReportResponse;
import com.drex.core.api.response.VideoSessionInitResponse;
import com.drex.core.dal.tablestore.builder.VideoViewingSessionBuilder;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.model.youtube.YouTubeBusinessCode;
import com.drex.core.service.business.video.DataValidationService;
import com.drex.core.service.business.video.RealtimeRewardService;
import com.drex.core.service.business.video.SessionProcessingService;
import com.drex.core.service.cache.SessionKeyCacheService;
import com.drex.core.service.cache.VideoViewingSessionCacheService;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.config.YouTubeRewardProperties;
import com.kikitrade.framework.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * YouTube防刷系统Dubbo服务实现
 */
@Slf4j
@DubboService
public class RemoteVideoAntiCheatServiceImpl implements RemoteVideoAntiCheatService {

    @Autowired
    private SessionProcessingService sessionProcessingService;

    @Autowired
    private DataValidationService dataValidationService;

    @Autowired
    private RealtimeRewardService realtimeRewardService;

    @Autowired
    private VideoViewingSessionBuilder videoViewingSessionBuilder;

    @Autowired
    private VideoViewingSessionCacheService videoViewingSessionCacheService;

    @Autowired
    private SessionKeyCacheService sessionKeyCacheService;

    @Autowired
    private YouTubeRewardProperties youTubeRewardProperties;

    @Override
    public Response<VideoSessionInitResponse> initVideoSession(VideoSessionInitRequest request) {
        try {
            log.info("Initializing video session for user: {}, video: {}",
                    request.getCustomerId(), request.getVideoId());

            // 调用会话处理服务
            VideoSessionInitResponse response = sessionProcessingService.initializeSession(request);

            if (response.getSuccess()) {
                return Response.success(response);
            } else {
                return Response.error(YouTubeBusinessCode.DATA_VALIDATION_FAILED.name(), response.getFailureReason());
            }

        } catch (Exception e) {
            log.error("Failed to initialize video session", e);
            VideoSessionInitResponse errorResponse = VideoSessionInitResponse.builder()
                    .success(false)
                    .failureReason("Internal server error")
                    .build();
            return Response.error(YouTubeBusinessCode.UNKNOWN_ERROR.name(), "Internal server error");
        }
    }

    @Override
    public Response<VideoReportResponse> reportEvents(VideoReportRequest request) {
        try {
            log.info("Processing event report for session: {}, events count: {}",
                    request.getSessionId(), request.getEvents().size());

            // 1. 数据验证
            DataValidationService.ValidationResult validationResult =
                    dataValidationService.validateReportRequest(request);

            if (!validationResult.isValid()) {
                return Response.error(YouTubeBusinessCode.DATA_VALIDATION_FAILED.name(),
                        validationResult.getErrorMessage());
            }

            // 2. 从缓存获取会话密钥
            String sessionKey = sessionKeyCacheService.getSessionKey(request.getSessionId());
            if (sessionKey == null) {
                log.warn("Session key not found in cache for session: {}", request.getSessionId());
                // 降级处理：重新生成密钥
                sessionKey = sessionProcessingService.generateSessionKey(request.getSessionId(), request.getCustomerId());
            }

            // 3. 解密和验证事件数据
            List<SessionEvent> events = dataValidationService.validateAndDecryptEvents(
                    request, request.getSignature(), sessionKey);

            if (events.isEmpty()) {
                VideoReportResponse errorResponse = VideoReportResponse.builder()
                        .success(false)
                        .failureReason("Failed to decrypt or validate events")
                        .build();
                return Response.error(YouTubeBusinessCode.DECRYPTION_FAILED.name(),
                        "Failed to decrypt events");
            }

            // 4. 更新会话
            SessionProcessingService.SessionUpdateResult updateResult =
                    sessionProcessingService.updateSession(request.getSessionId(), events);

            if (!updateResult.isSuccess()) {
                VideoReportResponse errorResponse = VideoReportResponse.builder()
                        .success(false)
                        .failureReason(updateResult.getErrorMessage())
                        .build();
                return Response.error(YouTubeBusinessCode.SESSION_EXPIRED.name(),
                        updateResult.getErrorMessage());
            }

            // 5. 实时奖励处理
            RealtimeRewardService.RealtimeProcessingResult rewardResult = processRealtimeReward(
                    request, updateResult);

            // 6. 构建响应
            VideoReportResponse response = buildReportResponse(request.getSessionId(), rewardResult);
            return Response.success(response);

        } catch (Exception e) {
            log.error("Failed to process event report", e);
            VideoReportResponse errorResponse = VideoReportResponse.builder()
                    .success(false)
                    .failureReason("Internal server error")
                    .build();
            return Response.error(YouTubeBusinessCode.UNKNOWN_ERROR.name(), "Internal server error");
        }
    }


    /**
     * 处理实时奖励
     */
    private RealtimeRewardService.RealtimeProcessingResult processRealtimeReward(
            VideoReportRequest request,
            SessionProcessingService.SessionUpdateResult updateResult) {
        try {
            // 从缓存中获取会话信息
            VideoViewingSession session = videoViewingSessionCacheService.getBySessionId(request.getSessionId());

            // 调用实时奖励服务
            return realtimeRewardService.processRealtimeEvents(
                    request.getCustomerId(),
                    session,
                    updateResult.getAllEvents(),
                    request.getSocialPlatform(),
                    request.getSocialEvent()
            );
        } catch (Exception e) {
            log.error("Failed to process realtime reward for session: {}", request.getSessionId(), e);
            return new RealtimeRewardService.RealtimeProcessingResult(false, "Reward processing failed");
        }
    }

    private VideoReportResponse buildReportResponse(String sessionId,
                                                    RealtimeRewardService.RealtimeProcessingResult realtimeResult) {

        VideoReportResponse.VideoReportResponseBuilder responseBuilder = VideoReportResponse.builder();
        responseBuilder.success(true);

        // 获取视频URL用于判断长短视频
        String videoUrl = getVideoUrlFromSession(sessionId);

        // 使用动态进度计算逻辑
        Integer playProgress = youTubeRewardProperties.calculateDynamicProgress(realtimeResult.getWatchPercentage(), videoUrl);

        // 设置播放进度
        responseBuilder.playProgress(playProgress);

        // 添加奖励信息
        if (realtimeResult != null && realtimeResult.getRewardInfo() != null) {
            var rewardInfo = realtimeResult.getRewardInfo();
            responseBuilder.rewardInfo(VideoReportResponse.RewardInfo.builder()
                    .rewardCode(rewardInfo.getRewardCode())
                    .rewardAmount(rewardInfo.getRewardAmount())
                    .rewardLevel(rewardInfo.getRewardLevel())
                    .progress(rewardInfo.getProgress())
                    .rewardStatus(rewardInfo.getRewardStatus())
                    .build());
        }

        return responseBuilder.build();
    }

    /**
     * 从会话中获取视频时长（优先从缓存获取）
     */
    private int getVideoDurationFromSession(String sessionId) {
        try {
            VideoViewingSession session = videoViewingSessionCacheService.getBySessionId(sessionId);
            return session.getVideoDurationSeconds();
        } catch (Exception e) {
            log.warn("Failed to get video duration for session: {}, using default", sessionId);
            return 300;
        }
    }

    /**
     * 从会话中获取视频URL（优先从缓存获取）
     */
    private String getVideoUrlFromSession(String sessionId) {
        try {
            VideoViewingSession session = videoViewingSessionCacheService.getBySessionId(sessionId);
            return session.getVideoUrl();
        } catch (Exception e) {
            log.warn("Failed to get video URL for session: {}, using default", sessionId);
            return ""; // 默认返回空字符串，会被判断为长视频
        }
    }

    /**
     * 从会话中获取视频ID（需要根据实际实现调整）
     */
    private String getVideoIdFromSession(String sessionId) {
        try {
            // 这里应该从会话缓存或数据库中获取视频ID
            // 暂时返回默认值
            return "default-video-id";
        } catch (Exception e) {
            log.warn("Failed to get video ID for session: {}, using default", sessionId);
            return "default-video-id";
        }
    }

}
