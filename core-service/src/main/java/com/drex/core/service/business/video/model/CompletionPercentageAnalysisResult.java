package com.drex.core.service.business.video.model;

import lombok.Data;

/**
 * 完成百分比分析结果
 */
@Data
public class CompletionPercentageAnalysisResult {
    private double completionPercentage;
    private boolean isAbnormalCompletion;
    private int totalPlaySeconds;
    private int effectivePlaySeconds;
    private double playEfficiency; // 有效播放时间 / 总播放时间

    public CompletionPercentageAnalysisResult(double completionPercentage, int totalPlaySeconds,
                                            int effectivePlaySeconds) {
        this.completionPercentage = completionPercentage;
        this.totalPlaySeconds = totalPlaySeconds;
        this.effectivePlaySeconds = effectivePlaySeconds;
        this.playEfficiency = effectivePlaySeconds > 0 ?
            (double) effectivePlaySeconds / totalPlaySeconds : 0.0;
    }

}