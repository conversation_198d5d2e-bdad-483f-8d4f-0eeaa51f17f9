package com.drex.core.service.business.video.model;

import lombok.Data;

import java.util.List;

/**
 * 播放速率分析结果
 */
@Data
public class PlaybackRateAnalysisResult {
    private double averagePlaybackRate;
    private double maxPlaybackRate;
    private double minPlaybackRate;
    private int rateChangeCount;
    private boolean hasAbnormalRate;
    private double abnormalRatePercentage;
    private List<PlaybackRateEvent> rateEvents;

    public PlaybackRateAnalysisResult(double averagePlaybackRate, double maxPlaybackRate,
                                    double minPlaybackRate, int rateChangeCount) {
        this.averagePlaybackRate = averagePlaybackRate;
        this.maxPlaybackRate = maxPlaybackRate;
        this.minPlaybackRate = minPlaybackRate;
        this.rateChangeCount = rateChangeCount;
    }

}