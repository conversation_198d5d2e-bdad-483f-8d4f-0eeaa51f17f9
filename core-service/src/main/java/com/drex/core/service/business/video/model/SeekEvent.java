package com.drex.core.service.business.video.model;

import lombok.Data;

/**
 * 跳转事件
 */
@Data
public class SeekEvent {
    private long timestamp;
    private double fromTime;
    private double toTime;
    private double distance;

    public SeekEvent(long timestamp, double fromTime, double toTime) {
        this.timestamp = timestamp;
        this.fromTime = fromTime;
        this.toTime = toTime;
        this.distance = Math.abs(toTime - fromTime);
    }

}