package com.drex.core.service.business.video.model;

import lombok.Data;

import java.util.List;

/**
 * 跳转行为分析结果
 */
@Data
public class SeekingBehaviorAnalysisResult {
    private int totalSeekCount;
    private double averageSeekDistance;
    private int forwardSeekCount;
    private int backwardSeekCount;
    private boolean hasAbnormalSeeking;
    private double seekFrequency; // 每分钟跳转次数
    private List<SeekEvent> seekEvents;

    public SeekingBehaviorAnalysisResult(int totalSeekCount, double averageSeekDistance) {
        this.totalSeekCount = totalSeekCount;
        this.averageSeekDistance = averageSeekDistance;
    }
}