package com.drex.core.service.business.video.worker;

import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.service.business.video.EnvironmentAnalysisService;
import com.drex.core.service.business.video.impl.FraudIndicatorsCalculator;
import com.drex.core.service.business.video.impl.FraudScoreCalculator;
import com.drex.core.service.business.video.model.FraudIndexResult;
import com.drex.core.service.business.video.model.RiskIndicators;
import com.drex.core.service.business.video.model.VideoIndicatorConfig;
import com.drex.core.service.cache.SessionEventCacheService;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.config.VideoRiskProperties;
import com.drex.core.service.util.async.callback.ICallback;
import com.drex.core.service.util.async.callback.IWorker;
import com.drex.core.service.util.async.worker.WorkResult;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 风险分数计算Worker
 * 负责计算欺诈风险分数（分值范围 0-100，分数越高风险越高）
 */
@Slf4j
@Component
public class FraudCalculationWorker implements IWorker<VideoViewingSession, FraudIndexResult>,
        ICallback<VideoViewingSession, FraudIndexResult> {

    @Resource
    private VideoRiskProperties videoRiskProperties;
    @Resource
    private SessionEventCacheService sessionEventCacheService;
    @Resource
    private EnvironmentAnalysisService environmentAnalysisService;

    @Override
    public FraudIndexResult action(VideoViewingSession viewingSession, Map<String, WorkerWrapper> allWrappers) {
        return execute(viewingSession);
    }

    public FraudIndexResult execute(VideoViewingSession viewingSession) {
        try {
            VideoIndicatorConfig videoIndicatorConfig = videoRiskProperties.getVideoIndicatorConfig();
            log.info("Calculating risk score for session: {}, videoIndicatorConfig: {}", viewingSession, videoIndicatorConfig);
            List<SessionEvent> allEvents = sessionEventCacheService.getEvents(viewingSession.getSessionId());

            allEvents.sort((o1, o2) -> o2.getClientTimestamp().compareTo(o1.getClientTimestamp()));
            SessionEvent sessionEvent = allEvents.get(0);
            String fingerprint = sessionEvent.getDeviceFinger();
            String ipAddress = sessionEvent.getSourceIp();

            // 1、计算欺诈指标
            RiskIndicators riskIndicators = FraudIndicatorsCalculator.calculateFraudIndicators(allEvents, videoIndicatorConfig);
            log.info("Calculated risk indicators result riskIndicators: {}", riskIndicators);

            // 指纹重复指标
            double browserFingerprint = environmentAnalysisService.analyzeBrowserFingerprint(fingerprint, viewingSession.getCustomerId(), ipAddress);
            riskIndicators.setFingerprintDuplication(browserFingerprint);

            // 恶意IP指标
            EnvironmentAnalysisService.IpReputationAnalysisResult ipResult = environmentAnalysisService.analyzeIpReputation(ipAddress);
            riskIndicators.setMaliciousIp(ipResult.isMalicious() ? 1.0 : 0.0);

            // 环境不一致性指标
            double environmentConsistency = environmentAnalysisService.analyzeEnvironmentConsistency(viewingSession.getCustomerId(), fingerprint, ipAddress);
            riskIndicators.setEnvironmentInconsistency(environmentConsistency);

            // 2、计算欺诈分数
            log.info("Calculating risk score for session: {}, videoIndicatorConfig: {}", viewingSession, videoIndicatorConfig);
            FraudIndexResult fraudIndexResult = FraudScoreCalculator.calculateFraudScore(riskIndicators, videoIndicatorConfig);
            log.info("Calculated risk score result fraudIndexResult: {}", fraudIndexResult);

            // 返回视频时长/观看时长
            fraudIndexResult.setEffectiveWatchSeconds(FraudIndicatorsCalculator.calculateEffectiveWatchTime(allEvents));
            fraudIndexResult.setVideoDurationSeconds(viewingSession.getVideoDurationSeconds());

            // 返回本次计算使用的欺诈指标/阈值/权重
            fraudIndexResult.setIndicators(riskIndicators);
            fraudIndexResult.setVideoIndicatorConfig(videoIndicatorConfig);

            return fraudIndexResult;
        } catch (Exception e) {
            if (viewingSession == null || !viewingSession.getSessionId().startsWith("test-")) {
                log.error("Failed to calculate risk score for session: {}", viewingSession != null ? viewingSession.getSessionId() : "null", e);
            } else {
                log.debug("Test case: Failed to calculate risk score for test session: {}", viewingSession.getSessionId());
            }
            double defaultRiskScore = videoRiskProperties.getDefaultRiskScore();
            return new FraudIndexResult(defaultRiskScore, defaultRiskScore, defaultRiskScore);
        }
    }



    @Override
    public void begin() {
        log.debug("RiskScoreCalculationWorker begin");
    }

    @Override
    public void result(boolean success, VideoViewingSession viewingSession, WorkResult<FraudIndexResult> result) {
        if (success) {
            log.debug("RiskScoreCalculationWorker completed successfully for session: {}, riskScore: {}",
                    viewingSession.getSessionId(), result);
        } else {
            log.error("RiskScoreCalculationWorker failed for session: {}", viewingSession.getSessionId());
        }
    }

}
