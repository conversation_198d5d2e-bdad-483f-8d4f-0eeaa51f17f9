package com.drex.core.service.business.video.worker;

import com.drex.core.api.request.GenerateMaizeRequest;
import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.request.SocialEventBody;
import com.drex.core.api.response.MaizeDTO;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.service.business.rexy.MaizeService;
import com.drex.core.service.business.video.RealtimeRewardService;
import com.drex.core.service.business.video.model.FraudIndexResult;
import com.drex.core.service.config.YouTubeRewardProperties;
import com.drex.core.service.util.async.callback.ICallback;
import com.drex.core.service.util.async.callback.IWorker;
import com.drex.core.service.util.async.worker.WorkResult;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 奖励计算Worker
 * 负责生成奖励代码和积分
 */
@Slf4j
@Component
public class RewardCalculationWorker implements IWorker<RewardCalculationWorker.RewardCalculationParam, RealtimeRewardService.RewardGenerationResult>,
        ICallback<RewardCalculationWorker.RewardCalculationParam, RealtimeRewardService.RewardGenerationResult> {

    @Autowired
    private YouTubeRewardProperties youTubeRewardProperties;
    @Resource
    private MaizeService maizeService;
    @Resource
    private MaizeRecordBuilder maizeRecordBuilder;

    @Override
    public RealtimeRewardService.RewardGenerationResult action(RewardCalculationParam param, 
                                                              Map<String, WorkerWrapper> allWrappers) {
        try {
            WorkResult<FraudIndexResult> riskScoreWorkerResult = allWrappers.get("riskScoreWorkerWrapper").getWorkResult();
            WorkResult<EventProcessingWorker.EventProcessingResult> eventWorkerWrapper = allWrappers.get("eventWorkerWrapper").getWorkResult();
            EventProcessingWorker.EventProcessingResult eventProcessingResult = eventWorkerWrapper.getResult();
            FraudIndexResult fraudIndexResult = riskScoreWorkerResult.getResult();


            Double riskScore = fraudIndexResult.getFinalFraudScore();
            Integer progress = eventProcessingResult.getCurrentProgress();
            double watchPercentage = eventProcessingResult.getWatchPercentage();

            boolean receiveReward = canReceiveReward(param.getCustomerId(), param.getSessionId(), param.getVideoId(), progress, param.getSocialEvent().name());
            if(!receiveReward){
                return null;
            }
            log.debug("Calculating reward for session: {}, progress: {}, riskScore: {}", 
                    param.getSessionId(), progress, riskScore);
            // 1. 获取奖励规则配置
            YouTubeRewardProperties.ProgressStage progressStage = youTubeRewardProperties.getProgressStage(
                    progress, param.getVideoUrl());
            if (progressStage == null) {
                return new RealtimeRewardService.RewardGenerationResult(false,
                        "Progress stage not found for progress: " + progress);
            }

            // 2. 检查风险分数要求（风险分数越高，越不应该给奖励）
            // 使用统一的风险分数阈值，超过阈值不发放奖励
            Integer riskScoreThreshold = youTubeRewardProperties.getGlobal().getRiskScoreThreshold();
            if (riskScore > riskScoreThreshold) {
                return new RealtimeRewardService.RewardGenerationResult(false,
                        String.format("Risk score too high: %.1f > %d", riskScore, riskScoreThreshold));
            }

            // 3. 构建生成玉米请求，包含新的字段
            GenerateMaizeRequest generateRequest = GenerateMaizeRequest.builder()
                    .customerId(param.getCustomerId())
                    .socialPlatform(param.getSocialPlatform())
                    .socialEvent(param.getSocialEvent())
                    .socialEventBody(SocialEventBody.builder()
                            .socialContentId(param.getVideoId())
                            .videoDuration((long) param.getVideoDurationSeconds())
                            .watchedDuration((long) (watchPercentage * param.getVideoDurationSeconds()))
                            .build())
                    .progress(progress)
                    .sessionId(param.getSessionId())
                    .build();

            MaizeDTO maizeDTO = maizeService.generateMaize(generateRequest);

            // 5. 构建返回结果
            RealtimeRewardService.RewardGenerationResult result = new RealtimeRewardService.RewardGenerationResult(
                    true, "Reward generated successfully");
            result.setRewardCode(maizeDTO.getCode());
            result.setRewardAmount(progressStage.getRewardAmount());
            result.setRewardLevel(progressStage.getRewardLevel());

            log.info("Reward generated successfully for session: {}, progress: {}, code: {}, score: {}",
                    param.getSessionId(), progress, maizeDTO.getCode(), maizeDTO.getScore());

            return result;

        } catch (Exception e) {
            log.error("Failed to generate reward for session: {}", param.getSessionId(), e);
            return new RealtimeRewardService.RewardGenerationResult(false, "Failed to generate reward: " + e.getMessage());
        }
    }

    @Override
    public void begin() {
        log.debug("RewardCalculationWorker begin");
    }

    @Override
    public void result(boolean success, RewardCalculationParam param, WorkResult<RealtimeRewardService.RewardGenerationResult> result) {
        if (success) {
            log.debug("RewardCalculationWorker completed successfully for session: {}", param.getSessionId());
        } else {
            log.error("RewardCalculationWorker failed for session: {}", param.getSessionId());
        }
    }


    /**
     * 奖励计算参数
     */
    @Data
    public static class RewardCalculationParam {
        private final String sessionId;
        private final String customerId;
        private final String videoId;
        private final String videoUrl;
        private final SocialConstant.PlatformEnum socialPlatform;
        private final SocialConstant.EventEnum socialEvent;
        private final int videoDurationSeconds;

        public RewardCalculationParam(String sessionId, String customerId, String videoId, String videoUrl,SocialConstant.PlatformEnum socialPlatform,
                                      SocialConstant.EventEnum socialEvent, Integer videoDurationSeconds) {
            this.sessionId = sessionId;
            this.customerId = customerId;
            this.videoId = videoId;
            this.videoUrl = videoUrl;
            this.socialPlatform = socialPlatform;
            this.socialEvent = socialEvent;
            this.videoDurationSeconds = videoDurationSeconds;
        }
    }

    private boolean canReceiveReward(String customerId, String sessionId, String videoId, Integer progress, String socialEvent) {
        try {
            // 使用组合键查询maizeRecord表检查是否已发放奖励
            // customerId + socialEvent + socialPlatform + socialContentId + sessionId + progress
            MaizeRecord existingRecord = maizeRecordBuilder.findMaizeRecordByCompositeKey(
                    customerId, socialEvent, videoId, sessionId, progress);

            return existingRecord == null;

        } catch (Exception e) {
            log.error("Failed to check reward eligibility for session: {}, progress: {}", sessionId, progress, e);
            return false;
        }
    }
}
