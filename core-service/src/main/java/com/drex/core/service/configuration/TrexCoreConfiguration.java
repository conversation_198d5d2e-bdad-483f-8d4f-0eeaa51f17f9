package com.drex.core.service.configuration;

import cn.hutool.core.util.ReflectUtil;
import com.aliyuncs.DefaultAcsClient;
import com.kikitrade.framework.kms.KmsClient;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Field;
import java.util.Map;

@Slf4j
@Configuration
public class TrexCoreConfiguration {

    @SneakyThrows
    @Bean
    @ConditionalOnProperty(value = "kiki.kms.enable", havingValue = "true")
    public DefaultAcsClient getDefaultAcsClient(KmsClient kmsClient) {
        Map<String, Field> fieldMap = ReflectUtil.getFieldMap(kmsClient.getClass());
        Field fd = fieldMap.get("defaultAcsClient");
        fd.setAccessible(true);
        return (DefaultAcsClient) fd.get(kmsClient);
    }


}
