package com.drex.core.service.util;

import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.request.VideoReportRequest;
import com.drex.core.service.cache.model.SessionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 事件数据转换工具类
 * 用于在VideoReportRequest.EventData和SessionEvent之间进行转换
 */
@Slf4j
@Component
public class EventDataConverter {

    /**
     * 将VideoReportRequest.EventData转换为SessionEvent
     *
     * @param requestEventData VideoReportRequest中的事件数据
     * @param sessionId 会话ID
     * @param customerId 用户ID
     * @param socialPlatform 社交平台
     * @param socialEvent 社交事件
     * @param deviceFinger 设备指纹
     * @param sourceIp 来源IP
     * @return 转换后的SessionEvent
     */
    public SessionEvent convertToSessionEvent(VideoReportRequest.EventData requestEventData,
                                              String sessionId,
                                              String customerId,
                                              SocialConstant.PlatformEnum socialPlatform,
                                              SocialConstant.EventEnum socialEvent,
                                              String deviceFinger,
                                              String sourceIp) {
        try {
            SessionEvent.SessionEventBuilder builder = SessionEvent.builder()
                    .sessionId(sessionId)
                    .customerId(customerId)
                    .clientTimestamp(requestEventData.getTimestamp())
                    .serverTimestamp(System.currentTimeMillis())
                    .eventType(requestEventData.getEventType())
                    .socialPlatform(socialPlatform)
                    .socialEvent(socialEvent)
                    .deviceFinger(deviceFinger)
                    .sourceIp(sourceIp);

            // 转换事件详细数据
            if (requestEventData.getDetails() != null) {
                SessionEvent.EventDetails sessionEventDetails = convertEventDetails(requestEventData.getDetails());
                builder.details(sessionEventDetails);
            }

            // 为了向后兼容，同时生成eventData Map格式
            Map<String, Object> eventDataMap = convertToEventDataMap(requestEventData);
            builder.eventData(eventDataMap);

            return builder.build();

        } catch (Exception e) {
            log.error("Failed to convert VideoReportRequest.EventData to SessionEvent: {}", 
                    requestEventData.getTimestamp(), e);
            throw new RuntimeException("Event data conversion failed", e);
        }
    }

    /**
     * 批量转换VideoReportRequest.EventData列表为SessionEvent列表
     */
    public List<SessionEvent> convertToSessionEvents(List<VideoReportRequest.EventData> requestEventDataList,
                                                     String sessionId,
                                                     String customerId,
                                                     SocialConstant.PlatformEnum socialPlatform,
                                                     SocialConstant.EventEnum socialEvent,
                                                     String deviceFinger,
                                                     String sourceIp) {
        List<SessionEvent> sessionEvents = new ArrayList<>();
        
        for (VideoReportRequest.EventData requestEventData : requestEventDataList) {
            SessionEvent sessionEvent = convertToSessionEvent(
                    requestEventData, sessionId, customerId, socialPlatform, socialEvent, deviceFinger, sourceIp);
            sessionEvents.add(sessionEvent);
        }
        
        return sessionEvents;
    }

    /**
     * 转换事件详细数据
     */
    private SessionEvent.EventDetails convertEventDetails(VideoReportRequest.EventDetails requestDetails) {
        SessionEvent.EventDetails.EventDetailsBuilder builder = SessionEvent.EventDetails.builder();

        // 转换PLAYING事件数据
        if (requestDetails.getPlayingData() != null) {
            SessionEvent.PlayingEventData playingData = SessionEvent.PlayingEventData.builder()
                    .newState(requestDetails.getPlayingData().getNewState())
                    .currentTime(requestDetails.getPlayingData().getCurrentTime())
                    .playbackRate(requestDetails.getPlayingData().getPlaybackRate())
                    .build();
            builder.playingData(playingData);
        }

        // 转换PAUSED事件数据
        if (requestDetails.getPausedData() != null) {
            SessionEvent.PausedEventData pausedData = SessionEvent.PausedEventData.builder()
                    .newState(requestDetails.getPausedData().getNewState())
                    .currentTime(requestDetails.getPausedData().getCurrentTime())
                    .build();
            builder.pausedData(pausedData);
        }

        // 转换SEEK事件数据
        if (requestDetails.getSeekData() != null) {
            SessionEvent.SeekEventData seekData = SessionEvent.SeekEventData.builder()
                    .currentTime(requestDetails.getSeekData().getCurrentTime())
                    .previousTime(requestDetails.getSeekData().getPreviousTime())
                    .build();
            builder.seekData(seekData);
        }

        // 转换FOCUS_LOST事件数据
        if (requestDetails.getFocusLostData() != null) {
            SessionEvent.FocusLostEventData focusLostData = SessionEvent.FocusLostEventData.builder()
                    .tabActive(requestDetails.getFocusLostData().getTabActive())
                    .windowFocused(requestDetails.getFocusLostData().getWindowFocused())
                    .build();
            builder.focusLostData(focusLostData);
        }

        // 转换FOCUS_GAINED事件数据
        if (requestDetails.getFocusGainedData() != null) {
            SessionEvent.FocusGainedEventData focusGainedData = SessionEvent.FocusGainedEventData.builder()
                    .tabActive(requestDetails.getFocusGainedData().getTabActive())
                    .windowFocused(requestDetails.getFocusGainedData().getWindowFocused())
                    .build();
            builder.focusGainedData(focusGainedData);
        }

        // 转换USER_STATE事件数据
        if (requestDetails.getUserStateData() != null) {
            SessionEvent.UserStateEventData userStateData = SessionEvent.UserStateEventData.builder()
                    .state(requestDetails.getUserStateData().getState())
                    .build();
            builder.userStateData(userStateData);
        }

        return builder.build();
    }

    /**
     * 为了向后兼容，将事件数据转换为Map格式
     */
    private Map<String, Object> convertToEventDataMap(VideoReportRequest.EventData requestEventData) {
        Map<String, Object> eventDataMap = new HashMap<>();
        
        if (requestEventData.getDetails() == null) {
            return eventDataMap;
        }

        VideoReportRequest.EventDetails details = requestEventData.getDetails();
        String eventType = requestEventData.getEventType();

        switch (eventType) {
            case "PLAYING":
                if (details.getPlayingData() != null) {
                    eventDataMap.put("newState", details.getPlayingData().getNewState());
                    eventDataMap.put("currentTime", details.getPlayingData().getCurrentTime());
                    eventDataMap.put("playbackRate", details.getPlayingData().getPlaybackRate());
                }
                break;
            case "PAUSED":
                if (details.getPausedData() != null) {
                    eventDataMap.put("newState", details.getPausedData().getNewState());
                    eventDataMap.put("currentTime", details.getPausedData().getCurrentTime());
                }
                break;
            case "SEEK":
                if (details.getSeekData() != null) {
                    eventDataMap.put("currentTime", details.getSeekData().getCurrentTime());
                    eventDataMap.put("previousTime", details.getSeekData().getPreviousTime());
                }
                break;
            case "FOCUS_LOST":
                if (details.getFocusLostData() != null) {
                    eventDataMap.put("tabActive", details.getFocusLostData().getTabActive());
                    eventDataMap.put("windowFocused", details.getFocusLostData().getWindowFocused());
                }
                break;
            case "FOCUS_GAINED":
                if (details.getFocusGainedData() != null) {
                    eventDataMap.put("tabActive", details.getFocusGainedData().getTabActive());
                    eventDataMap.put("windowFocused", details.getFocusGainedData().getWindowFocused());
                }
                break;
            case "USER_STATE":
                if (details.getUserStateData() != null) {
                    eventDataMap.put("state", details.getUserStateData().getState());
                }
                break;
            default:
                log.warn("Unknown event type: {}", eventType);
                break;
        }

        return eventDataMap;
    }

    /**
     * 将SessionEvent转换回VideoReportRequest.EventData（用于测试或其他需要）
     */
    public VideoReportRequest.EventData convertToRequestEventData(SessionEvent sessionEvent) {
        VideoReportRequest.EventData.EventDataBuilder builder = VideoReportRequest.EventData.builder()
                .eventType(sessionEvent.getEventType())
                .timestamp(sessionEvent.getClientTimestamp());

        // 转换事件详细数据
        if (sessionEvent.getDetails() != null) {
            VideoReportRequest.EventDetails requestDetails = convertToRequestEventDetails(sessionEvent.getDetails());
            builder.details(requestDetails);
        }

        return builder.build();
    }

    /**
     * 转换SessionEvent.EventDetails为VideoReportRequest.EventDetails
     */
    private VideoReportRequest.EventDetails convertToRequestEventDetails(SessionEvent.EventDetails sessionDetails) {
        VideoReportRequest.EventDetails.EventDetailsBuilder builder = VideoReportRequest.EventDetails.builder();

        // 转换各种事件数据
        if (sessionDetails.getPlayingData() != null) {
            VideoReportRequest.PlayingEventData playingData = VideoReportRequest.PlayingEventData.builder()
                    .newState(sessionDetails.getPlayingData().getNewState())
                    .currentTime(sessionDetails.getPlayingData().getCurrentTime())
                    .playbackRate(sessionDetails.getPlayingData().getPlaybackRate())
                    .build();
            builder.playingData(playingData);
        }

        if (sessionDetails.getPausedData() != null) {
            VideoReportRequest.PausedEventData pausedData = VideoReportRequest.PausedEventData.builder()
                    .newState(sessionDetails.getPausedData().getNewState())
                    .currentTime(sessionDetails.getPausedData().getCurrentTime())
                    .build();
            builder.pausedData(pausedData);
        }

        if (sessionDetails.getSeekData() != null) {
            VideoReportRequest.SeekEventData seekData = VideoReportRequest.SeekEventData.builder()
                    .currentTime(sessionDetails.getSeekData().getCurrentTime())
                    .previousTime(sessionDetails.getSeekData().getPreviousTime())
                    .build();
            builder.seekData(seekData);
        }

        if (sessionDetails.getFocusLostData() != null) {
            VideoReportRequest.FocusLostEventData focusLostData = VideoReportRequest.FocusLostEventData.builder()
                    .tabActive(sessionDetails.getFocusLostData().getTabActive())
                    .windowFocused(sessionDetails.getFocusLostData().getWindowFocused())
                    .build();
            builder.focusLostData(focusLostData);
        }

        if (sessionDetails.getFocusGainedData() != null) {
            VideoReportRequest.FocusGainedEventData focusGainedData = VideoReportRequest.FocusGainedEventData.builder()
                    .tabActive(sessionDetails.getFocusGainedData().getTabActive())
                    .windowFocused(sessionDetails.getFocusGainedData().getWindowFocused())
                    .build();
            builder.focusGainedData(focusGainedData);
        }

        if (sessionDetails.getUserStateData() != null) {
            VideoReportRequest.UserStateEventData userStateData = VideoReportRequest.UserStateEventData.builder()
                    .state(sessionDetails.getUserStateData().getState())
                    .build();
            builder.userStateData(userStateData);
        }

        return builder.build();
    }
}
