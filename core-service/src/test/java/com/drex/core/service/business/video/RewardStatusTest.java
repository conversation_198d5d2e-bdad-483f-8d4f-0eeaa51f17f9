package com.drex.core.service.business.video;

import com.drex.core.api.common.RexyConstant;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.service.business.video.impl.RealtimeRewardServiceImpl;
import com.drex.core.service.config.YouTubeRewardProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 奖励状态计算测试
 */
@ExtendWith(MockitoExtension.class)
public class RewardStatusTest {

    @Mock
    private MaizeRecordBuilder maizeRecordBuilder;

    @Mock
    private YouTubeRewardProperties youTubeRewardProperties;

    @InjectMocks
    private RealtimeRewardServiceImpl realtimeRewardService;

    private String customerId = "test_customer";
    private String sessionId = "test_session";
    private String videoId = "test_video";
    private String socialEvent = "watch";
    private String videoUrl = "https://youtube.com/watch?v=test";

    @BeforeEach
    void setUp() {
        // 模拟YouTube奖励配置
        when(youTubeRewardProperties.calculateProgress(anyDouble(), anyString())).thenAnswer(invocation -> {
            Double watchPercentage = invocation.getArgument(0);
            if (watchPercentage >= 0.2 && watchPercentage <= 0.4) {
                return 1;
            } else if (watchPercentage >= 0.5 && watchPercentage <= 0.7) {
                return 2;
            } else if (watchPercentage >= 0.8 && watchPercentage <= 1.2) {
                return 3;
            }
            return null;
        });
    }

    @Test
    void testRewardStatusInRewardRange_Unclaimed() {
        // 测试：当前进度在奖励范围内，奖励未领取
        Integer currentProgress = 2;
        Double watchPercentage = 0.6; // 在第二阶段范围内

        // 模拟没有找到奖励记录（未发放）
        when(maizeRecordBuilder.findMaizeRecordByCompositeKey(
                customerId, socialEvent, videoId, sessionId, currentProgress))
                .thenReturn(null);

        String result = realtimeRewardService.calculateRewardStatus(
                customerId, sessionId, videoId, currentProgress, watchPercentage, videoUrl, socialEvent);

        assertEquals(RexyConstant.MaizeStatus.UNCLAIMED.name(), result);
    }

    @Test
    void testRewardStatusInRewardRange_Claimed() {
        // 测试：当前进度在奖励范围内，奖励已领取
        Integer currentProgress = 2;
        Double watchPercentage = 0.6;

        MaizeRecord claimedRecord = new MaizeRecord();
        claimedRecord.setStatus(RexyConstant.MaizeStatus.CLAIMED.name());
        claimedRecord.setExpireTime(System.currentTimeMillis() + 86400000L); // 未过期

        when(maizeRecordBuilder.findMaizeRecordByCompositeKey(
                customerId, socialEvent, videoId, sessionId, currentProgress))
                .thenReturn(claimedRecord);

        String result = realtimeRewardService.calculateRewardStatus(
                customerId, sessionId, videoId, currentProgress, watchPercentage, videoUrl, socialEvent);

        assertEquals(RexyConstant.MaizeStatus.CLAIMED.name(), result);
    }

    @Test
    void testRewardStatusInRewardRange_Expired() {
        // 测试：当前进度在奖励范围内，奖励已过期
        Integer currentProgress = 2;
        Double watchPercentage = 0.6;

        MaizeRecord expiredRecord = new MaizeRecord();
        expiredRecord.setStatus(RexyConstant.MaizeStatus.ISSUED.name());
        expiredRecord.setExpireTime(System.currentTimeMillis() - 1000L); // 已过期

        when(maizeRecordBuilder.findMaizeRecordByCompositeKey(
                customerId, socialEvent, videoId, sessionId, currentProgress))
                .thenReturn(expiredRecord);

        String result = realtimeRewardService.calculateRewardStatus(
                customerId, sessionId, videoId, currentProgress, watchPercentage, videoUrl, socialEvent);

        assertEquals(RexyConstant.MaizeStatus.EXPIRED.name(), result);
    }

    @Test
    void testRewardStatusInBlankPeriod_PreviousClaimed() {
        // 测试：当前进度处于空白期，前一个阶段已领取
        Integer currentProgress = 2;
        Double watchPercentage = 0.45; // 在空白期（不在任何奖励范围内）

        // 模拟当前进度不在奖励范围内
        when(youTubeRewardProperties.calculateProgress(watchPercentage, videoUrl)).thenReturn(null);

        MaizeRecord previousRecord = new MaizeRecord();
        previousRecord.setStatus(RexyConstant.MaizeStatus.CLAIMED.name());
        previousRecord.setProgress(1);

        when(maizeRecordBuilder.findPreviousStageReward(
                customerId, socialEvent, videoId, sessionId, currentProgress))
                .thenReturn(previousRecord);

        String result = realtimeRewardService.calculateRewardStatus(
                customerId, sessionId, videoId, currentProgress, watchPercentage, videoUrl, socialEvent);

        assertEquals(RexyConstant.MaizeStatus.CLAIMED.name(), result);
    }

    @Test
    void testRewardStatusInBlankPeriod_PreviousExpired() {
        // 测试：当前进度处于空白期，前一个阶段已过期
        Integer currentProgress = 3;
        Double watchPercentage = 0.75; // 在空白期

        when(youTubeRewardProperties.calculateProgress(watchPercentage, videoUrl)).thenReturn(null);

        MaizeRecord previousRecord = new MaizeRecord();
        previousRecord.setStatus(RexyConstant.MaizeStatus.ISSUED.name());
        previousRecord.setProgress(2);
        previousRecord.setExpireTime(System.currentTimeMillis() - 1000L); // 已过期

        when(maizeRecordBuilder.findPreviousStageReward(
                customerId, socialEvent, videoId, sessionId, currentProgress))
                .thenReturn(previousRecord);

        String result = realtimeRewardService.calculateRewardStatus(
                customerId, sessionId, videoId, currentProgress, watchPercentage, videoUrl, socialEvent);

        assertEquals(RexyConstant.MaizeStatus.EXPIRED.name(), result);
    }

    @Test
    void testRewardStatusInBlankPeriod_NoPrevious() {
        // 测试：当前进度处于空白期，没有前一个阶段
        Integer currentProgress = 1;
        Double watchPercentage = 0.1; // 在空白期

        when(youTubeRewardProperties.calculateProgress(watchPercentage, videoUrl)).thenReturn(null);

        when(maizeRecordBuilder.findPreviousStageReward(
                customerId, socialEvent, videoId, sessionId, currentProgress))
                .thenReturn(null);

        String result = realtimeRewardService.calculateRewardStatus(
                customerId, sessionId, videoId, currentProgress, watchPercentage, videoUrl, socialEvent);

        assertEquals(RexyConstant.MaizeStatus.UNCLAIMED.name(), result);
    }
}
