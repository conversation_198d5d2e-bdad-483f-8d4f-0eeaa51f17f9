package com.drex.core.service.business.video;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.request.VideoReportRequest;
import com.drex.core.service.business.video.impl.DataValidationServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RexyReportRequest新事件类型测试
 */
@ExtendWith(MockitoExtension.class)
public class VideoReportRequestTest {

    @InjectMocks
    private DataValidationServiceImpl dataValidationService;

    private VideoReportRequest testRequest;

    @BeforeEach
    void setUp() {
        testRequest = createTestRexyReportRequest();
    }

    @Test
    void testRexyReportRequestStructure() {
        // 测试基本字段
        assertNotNull(testRequest.getSessionId());
        assertNotNull(testRequest.getCustomerId());
        assertNotNull(testRequest.getDeviceFinger());
        assertNotNull(testRequest.getEvents());
        assertEquals(6, testRequest.getEvents().size());
    }

    @Test
    void testPlayingEventData() {
        VideoReportRequest.EventData playingEvent = testRequest.getEvents().get(0);
        assertEquals("PLAYING", playingEvent.getEventType());
        assertNotNull(playingEvent.getDetails());
        assertNotNull(playingEvent.getDetails().getPlayingData());
        
        VideoReportRequest.PlayingEventData playingData = playingEvent.getDetails().getPlayingData();
        assertEquals("PLAYING", playingData.getNewState());
        assertEquals(120.5, playingData.getCurrentTime());
        assertEquals(1.0, playingData.getPlaybackRate());
    }

    @Test
    void testPausedEventData() {
        VideoReportRequest.EventData pausedEvent = testRequest.getEvents().get(1);
        assertEquals("PAUSED", pausedEvent.getEventType());
        assertNotNull(pausedEvent.getDetails());
        assertNotNull(pausedEvent.getDetails().getPausedData());
        
        VideoReportRequest.PausedEventData pausedData = pausedEvent.getDetails().getPausedData();
        assertEquals("PAUSED", pausedData.getNewState());
        assertEquals(125.0, pausedData.getCurrentTime());
    }

    @Test
    void testSeekEventData() {
        VideoReportRequest.EventData seekEvent = testRequest.getEvents().get(2);
        assertEquals("SEEK", seekEvent.getEventType());
        assertNotNull(seekEvent.getDetails());
        assertNotNull(seekEvent.getDetails().getSeekData());
        
        VideoReportRequest.SeekEventData seekData = seekEvent.getDetails().getSeekData();
        assertEquals(150.0, seekData.getCurrentTime());
        assertEquals(125.0, seekData.getPreviousTime());
    }

    @Test
    void testFocusLostEventData() {
        VideoReportRequest.EventData focusLostEvent = testRequest.getEvents().get(3);
        assertEquals("FOCUS_LOST", focusLostEvent.getEventType());
        assertNotNull(focusLostEvent.getDetails());
        assertNotNull(focusLostEvent.getDetails().getFocusLostData());
        
        VideoReportRequest.FocusLostEventData focusLostData = focusLostEvent.getDetails().getFocusLostData();
        assertEquals(false, focusLostData.getTabActive());
        assertEquals(true, focusLostData.getWindowFocused());
    }

    @Test
    void testFocusGainedEventData() {
        VideoReportRequest.EventData focusGainedEvent = testRequest.getEvents().get(4);
        assertEquals("FOCUS_GAINED", focusGainedEvent.getEventType());
        assertNotNull(focusGainedEvent.getDetails());
        assertNotNull(focusGainedEvent.getDetails().getFocusGainedData());
        
        VideoReportRequest.FocusGainedEventData focusGainedData = focusGainedEvent.getDetails().getFocusGainedData();
        assertEquals(true, focusGainedData.getTabActive());
        assertEquals(true, focusGainedData.getWindowFocused());
    }

    @Test
    void testUserStateEventData() {
        VideoReportRequest.EventData userStateEvent = testRequest.getEvents().get(5);
        assertEquals("USER_STATE", userStateEvent.getEventType());
        assertNotNull(userStateEvent.getDetails());
        assertNotNull(userStateEvent.getDetails().getUserStateData());
        
        VideoReportRequest.UserStateEventData userStateData = userStateEvent.getDetails().getUserStateData();
        assertEquals("ACTIVE", userStateData.getState());
    }

    @Test
    void testEventDataValidation() {
        DataValidationService.ValidationResult result = dataValidationService.validateReportRequest(testRequest);
        // 注意：由于我们没有设置encryptedData和signature，验证会失败
        // 这里主要测试事件结构的验证逻辑
        assertNotNull(result);
    }

    @Test
    void testJsonSerialization() {
        // 测试JSON序列化和反序列化
        String json = JSON.toJSONString(testRequest);
        assertNotNull(json);
        assertTrue(json.contains("PLAYING"));
        assertTrue(json.contains("PAUSED"));
        assertTrue(json.contains("SEEK"));
        assertTrue(json.contains("FOCUS_LOST"));
        assertTrue(json.contains("FOCUS_GAINED"));
        assertTrue(json.contains("USER_STATE"));
        
        // 反序列化测试
        VideoReportRequest deserializedRequest = JSON.parseObject(json, VideoReportRequest.class);
        assertNotNull(deserializedRequest);
        assertEquals(testRequest.getSessionId(), deserializedRequest.getSessionId());
        assertEquals(testRequest.getEvents().size(), deserializedRequest.getEvents().size());
    }

    /**
     * 创建测试用的RexyReportRequest
     */
    private VideoReportRequest createTestRexyReportRequest() {
        // 创建6种不同类型的事件
        List<VideoReportRequest.EventData> events = Arrays.asList(
                createPlayingEvent(),
                createPausedEvent(),
                createSeekEvent(),
                createFocusLostEvent(),
                createFocusGainedEvent(),
                createUserStateEvent()
        );

        return VideoReportRequest.builder()
                .sessionId("test-session-123")
                .customerId("test-customer-456")
                .deviceFinger("test-device-fingerprint-789")
                .clientTimestamp(System.currentTimeMillis())
                .signature("test-signature")
                .events(events)
                .build();
    }

    private VideoReportRequest.EventData createPlayingEvent() {
        VideoReportRequest.PlayingEventData playingData = VideoReportRequest.PlayingEventData.builder()
                .newState("PLAYING")
                .currentTime(120.5)
                .playbackRate(1.0)
                .build();

        VideoReportRequest.EventDetails details = VideoReportRequest.EventDetails.builder()
                .playingData(playingData)
                .build();

        return VideoReportRequest.EventData.builder()
                .eventType("PLAYING")
                .timestamp(System.currentTimeMillis())
                .details(details)
                .build();
    }

    private VideoReportRequest.EventData createPausedEvent() {
        VideoReportRequest.PausedEventData pausedData = VideoReportRequest.PausedEventData.builder()
                .newState("PAUSED")
                .currentTime(125.0)
                .build();

        VideoReportRequest.EventDetails details = VideoReportRequest.EventDetails.builder()
                .pausedData(pausedData)
                .build();

        return VideoReportRequest.EventData.builder()
                .eventType("PAUSED")
                .timestamp(System.currentTimeMillis())
                .details(details)
                .build();
    }

    private VideoReportRequest.EventData createSeekEvent() {
        VideoReportRequest.SeekEventData seekData = VideoReportRequest.SeekEventData.builder()
                .currentTime(150.0)
                .previousTime(125.0)
                .build();

        VideoReportRequest.EventDetails details = VideoReportRequest.EventDetails.builder()
                .seekData(seekData)
                .build();

        return VideoReportRequest.EventData.builder()
                .eventType("SEEK")
                .timestamp(System.currentTimeMillis())
                .details(details)
                .build();
    }

    private VideoReportRequest.EventData createFocusLostEvent() {
        VideoReportRequest.FocusLostEventData focusLostData = VideoReportRequest.FocusLostEventData.builder()
                .tabActive(false)
                .windowFocused(true)
                .build();

        VideoReportRequest.EventDetails details = VideoReportRequest.EventDetails.builder()
                .focusLostData(focusLostData)
                .build();

        return VideoReportRequest.EventData.builder()
                .eventType("FOCUS_LOST")
                .timestamp(System.currentTimeMillis())
                .details(details)
                .build();
    }

    private VideoReportRequest.EventData createFocusGainedEvent() {
        VideoReportRequest.FocusGainedEventData focusGainedData = VideoReportRequest.FocusGainedEventData.builder()
                .tabActive(true)
                .windowFocused(true)
                .build();

        VideoReportRequest.EventDetails details = VideoReportRequest.EventDetails.builder()
                .focusGainedData(focusGainedData)
                .build();

        return VideoReportRequest.EventData.builder()
                .eventType("FOCUS_GAINED")
                .timestamp(System.currentTimeMillis())
                .details(details)
                .build();
    }

    private VideoReportRequest.EventData createUserStateEvent() {
        VideoReportRequest.UserStateEventData userStateData = VideoReportRequest.UserStateEventData.builder()
                .state("ACTIVE")
                .build();

        VideoReportRequest.EventDetails details = VideoReportRequest.EventDetails.builder()
                .userStateData(userStateData)
                .build();

        return VideoReportRequest.EventData.builder()
                .eventType("USER_STATE")
                .timestamp(System.currentTimeMillis())
                .details(details)
                .build();
    }
}
