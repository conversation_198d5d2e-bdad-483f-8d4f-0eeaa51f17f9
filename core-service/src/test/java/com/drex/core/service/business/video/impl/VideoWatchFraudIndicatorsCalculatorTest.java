package com.drex.core.service.business.video.impl;

import com.drex.core.service.business.video.model.RiskIndicators;
import com.drex.core.service.business.video.model.VideoIndicatorConfig;
import com.drex.core.service.cache.model.SessionEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 视频观看欺诈指标计算器测试
 */
class VideoWatchFraudIndicatorsCalculatorTest {

    private VideoIndicatorConfig videoIndicatorConfig;
    private List<SessionEvent> testEvents;

    @BeforeEach
    void setUp() {
        // 创建测试配置
        videoIndicatorConfig = createTestConfig();
        
        // 创建测试事件
        testEvents = createTestEvents();
    }

    @Test
    void testCalculateFraudIndicatorsWithNewEventStructure() {
        // When
        RiskIndicators indicators = VideoWatchFraudIndicatorsCalculator.calculateFraudIndicators(
                testEvents, 300, videoIndicatorConfig);

        // Then
        assertNotNull(indicators);
        assertTrue(indicators.getRepeatedEvents() >= 0);
        assertTrue(indicators.getAbnormalCompletion() >= 0);
        assertTrue(indicators.getLowFocusDuration() >= 0);
        assertTrue(indicators.getLongIdleDuration() >= 0);
        assertTrue(indicators.getExcessivePlaybackSpeed() >= 0);
        assertTrue(indicators.getAbnormalSeek() >= 0);
        assertTrue(indicators.getEventOrderAnomaly() >= 0);
    }

    @Test
    void testCalculateFraudIndicatorsWithOldEventStructure() {
        // Given - 创建使用旧eventData格式的事件
        List<SessionEvent> oldFormatEvents = createOldFormatEvents();

        // When
        RiskIndicators indicators = VideoWatchFraudIndicatorsCalculator.calculateFraudIndicators(
                oldFormatEvents, 300, videoIndicatorConfig);

        // Then
        assertNotNull(indicators);
        assertTrue(indicators.getRepeatedEvents() >= 0);
        assertTrue(indicators.getAbnormalCompletion() >= 0);
    }

    @Test
    void testCalculateFraudIndicatorsWithMixedEventStructure() {
        // Given - 创建混合新旧格式的事件
        List<SessionEvent> mixedEvents = createMixedFormatEvents();

        // When
        RiskIndicators indicators = VideoWatchFraudIndicatorsCalculator.calculateFraudIndicators(
                mixedEvents, 300, videoIndicatorConfig);

        // Then
        assertNotNull(indicators);
        // 验证混合格式也能正确处理
        assertTrue(indicators.getRepeatedEvents() >= 0);
    }

    @Test
    void testCalculateFraudIndicatorsWithEmptyEvents() {
        // Given
        List<SessionEvent> emptyEvents = Arrays.asList();

        // When
        RiskIndicators indicators = VideoWatchFraudIndicatorsCalculator.calculateFraudIndicators(
                emptyEvents, 300, videoIndicatorConfig);

        // Then
        assertNotNull(indicators);
        assertEquals(0.0, indicators.getRepeatedEvents());
    }

    private VideoIndicatorConfig createTestConfig() {
        VideoIndicatorConfig config = new VideoIndicatorConfig();
        
        // 设置各项指标的配置
        VideoIndicatorConfig.RepeatedEventConfig repeatedEvent = new VideoIndicatorConfig.RepeatedEventConfig();
        repeatedEvent.setThreshold(5.0);
        repeatedEvent.setMaxAllowed(20.0);
        config.setRepeatedEvent(repeatedEvent);

        VideoIndicatorConfig.CompletionConfig completion = new VideoIndicatorConfig.CompletionConfig();
        completion.setExpectedPercentage(0.8);
        completion.setMaxAllowedPercentage(1.2);
        config.setCompletion(completion);

        VideoIndicatorConfig.FocusConfig focus = new VideoIndicatorConfig.FocusConfig();
        focus.setThreshold(0.7);
        focus.setMinAllowed(0.3);
        config.setFocus(focus);

        VideoIndicatorConfig.IdleConfig idle = new VideoIndicatorConfig.IdleConfig();
        idle.setThreshold(0.2);
        idle.setMaxAllowed(0.5);
        config.setIdle(idle);

        VideoIndicatorConfig.PlaybackSpeedConfig playbackSpeed = new VideoIndicatorConfig.PlaybackSpeedConfig();
        playbackSpeed.setThreshold(1.5);
        playbackSpeed.setMaxAllowed(3.0);
        config.setPlaybackSpeed(playbackSpeed);

        VideoIndicatorConfig.AbnormalSeekConfig abnormalSeek = new VideoIndicatorConfig.AbnormalSeekConfig();
        abnormalSeek.setThreshold(2.0);
        abnormalSeek.setMaxAllowed(10.0);
        config.setAbnormalSeek(abnormalSeek);

        return config;
    }

    private List<SessionEvent> createTestEvents() {
        long baseTime = System.currentTimeMillis();
        
        // 创建PLAYING事件（使用新的details结构）
        SessionEvent playingEvent = SessionEvent.builder()
                .eventType("PLAYING")
                .clientTimestamp(baseTime)
                .details(SessionEvent.EventDetails.builder()
                        .playingData(SessionEvent.PlayingEventData.builder()
                                .newState("PLAYING")
                                .currentTime(10.0)
                                .playbackRate(1.0)
                                .build())
                        .build())
                .build();

        // 创建PAUSED事件（使用新的details结构）
        SessionEvent pausedEvent = SessionEvent.builder()
                .eventType("PAUSED")
                .clientTimestamp(baseTime + 5000)
                .details(SessionEvent.EventDetails.builder()
                        .pausedData(SessionEvent.PausedEventData.builder()
                                .newState("PAUSED")
                                .currentTime(15.0)
                                .build())
                        .build())
                .build();

        // 创建SEEK事件（使用新的details结构）
        SessionEvent seekEvent = SessionEvent.builder()
                .eventType("SEEK")
                .clientTimestamp(baseTime + 10000)
                .details(SessionEvent.EventDetails.builder()
                        .seekData(SessionEvent.SeekEventData.builder()
                                .currentTime(30.0)
                                .previousTime(15.0)
                                .build())
                        .build())
                .build();

        // 创建FOCUS_LOST事件（使用新的details结构）
        SessionEvent focusLostEvent = SessionEvent.builder()
                .eventType("FOCUS_LOST")
                .clientTimestamp(baseTime + 15000)
                .details(SessionEvent.EventDetails.builder()
                        .focusLostData(SessionEvent.FocusLostEventData.builder()
                                .tabActive(false)
                                .windowFocused(false)
                                .build())
                        .build())
                .build();

        // 创建FOCUS_GAINED事件（使用新的details结构）
        SessionEvent focusGainedEvent = SessionEvent.builder()
                .eventType("FOCUS_GAINED")
                .clientTimestamp(baseTime + 20000)
                .details(SessionEvent.EventDetails.builder()
                        .focusGainedData(SessionEvent.FocusGainedEventData.builder()
                                .tabActive(true)
                                .windowFocused(true)
                                .build())
                        .build())
                .build();

        return Arrays.asList(playingEvent, pausedEvent, seekEvent, focusLostEvent, focusGainedEvent);
    }

    private List<SessionEvent> createOldFormatEvents() {
        long baseTime = System.currentTimeMillis();
        
        // 创建使用旧eventData格式的事件
        Map<String, Object> playingEventData = new HashMap<>();
        playingEventData.put("newState", "PLAYING");
        playingEventData.put("currentTime", 10.0);
        playingEventData.put("playbackRate", 1.0);

        SessionEvent oldPlayingEvent = SessionEvent.builder()
                .eventType("PLAYBACK_STATE_CHANGE")
                .clientTimestamp(baseTime)
                .eventData(playingEventData)
                .build();

        Map<String, Object> focusEventData = new HashMap<>();
        focusEventData.put("tabActive", false);
        focusEventData.put("windowFocused", false);

        SessionEvent oldFocusEvent = SessionEvent.builder()
                .eventType("FOCUS_CHANGE")
                .clientTimestamp(baseTime + 5000)
                .eventData(focusEventData)
                .build();

        return Arrays.asList(oldPlayingEvent, oldFocusEvent);
    }

    private List<SessionEvent> createMixedFormatEvents() {
        List<SessionEvent> newEvents = createTestEvents();
        List<SessionEvent> oldEvents = createOldFormatEvents();
        
        // 合并新旧格式事件
        return Arrays.asList(
                newEvents.get(0),  // 新格式PLAYING事件
                oldEvents.get(0),  // 旧格式PLAYBACK_STATE_CHANGE事件
                newEvents.get(1),  // 新格式PAUSED事件
                oldEvents.get(1)   // 旧格式FOCUS_CHANGE事件
        );
    }
}
